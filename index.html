<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chat Helper Bot - AI-Powered Document Assistant</title>
    <meta name="description" content="AI-powered chat assistant for your documents. Ask questions, get insights, and interact with your PDFs, notes, and documents." />
    <meta name="author" content="Chat Helper Bot" />
    <meta name="keywords" content="AI, chat, documents, PDF, assistant, Vue 3, TypeScript" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Chat Helper Bot - AI-Powered Document Assistant" />
    <meta property="og:description" content="AI-powered chat assistant for your documents. Ask questions, get insights, and interact with your PDFs, notes, and documents." />
    <meta property="og:image" content="/placeholder.svg" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Chat Helper Bot - AI-Powered Document Assistant" />
    <meta name="twitter:description" content="AI-powered chat assistant for your documents. Ask questions, get insights, and interact with your PDFs, notes, and documents." />
    <meta name="twitter:image" content="/placeholder.svg" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.svg" />

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#8b5cf6" />

    <!-- Preload critical fonts if any -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
