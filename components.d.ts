/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Avatar: typeof import('./src/components/ui/Avatar.vue')['default']
    Button: typeof import('./src/components/ui/Button.vue')['default']
    ChatHeader: typeof import('./src/components/ChatHeader.vue')['default']
    ChatInput: typeof import('./src/components/ChatInput.vue')['default']
    ChatInterface: typeof import('./src/components/ChatInterface.vue')['default']
    ChatMessage: typeof import('./src/components/ChatMessage.vue')['default']
    ChatSidebar: typeof import('./src/components/ChatSidebar.vue')['default']
    DocumentPreview: typeof import('./src/components/DocumentPreview.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    Input: typeof import('./src/components/ui/Input.vue')['default']
    LanguageSelector: typeof import('./src/components/LanguageSelector.vue')['default']
    MarkdownTest: typeof import('./src/components/MarkdownTest.vue')['default']
    ReasoningMessage: typeof import('./src/components/ReasoningMessage.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollArea: typeof import('./src/components/ui/ScrollArea.vue')['default']
    Tabs: typeof import('./src/components/ui/Tabs.vue')['default']
    Textarea: typeof import('./src/components/ui/Textarea.vue')['default']
  }
}
