<template>
  <div class="h-screen bg-background flex flex-col overflow-hidden">
    <div class="flex-1 flex overflow-hidden">
      <!-- Sidebar -->
      <Transition
        name="sidebar"
        enter-active-class="transition-transform duration-300 ease-out"
        leave-active-class="transition-transform duration-300 ease-in"
        enter-from-class="-translate-x-full"
        enter-to-class="translate-x-0"
        leave-from-class="translate-x-0"
        leave-to-class="-translate-x-full"
      >
        <ChatSidebar
          v-if="!isSidebarCollapsed"
          :is-sidebar-collapsed="isSidebarCollapsed"
          :selected-conversation="selectedSessionId"
          :mode="mode"
          @select-conversation="handleSelectSession"
          @new-conversation="handleNewSession"
          @mode-change="handleModeChange"
          @toggle-sidebar="handleToggleSidebar"
        />
      </Transition>
      
      <!-- Main Chat Area -->
      <ChatInterface
        :conversation-id="selectedSessionId"
        @conversation-change="handleSessionChange"
      />
    </div>

    <!-- Loading Overlay -->
    <Transition
      name="fade"
      enter-active-class="transition-opacity duration-200"
      leave-active-class="transition-opacity duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isInitializing"
        class="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div class="text-center">
          <Loader2 class="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p class="text-sm text-muted-foreground">{{ t('common.loading_conversations') }}</p>
        </div>
      </div>
    </Transition>

    <!-- Error Toast -->
    <Transition
      name="slide-up"
      enter-active-class="transition-transform duration-300 ease-out"
      leave-active-class="transition-transform duration-300 ease-in"
      enter-from-class="translate-y-full"
      enter-to-class="translate-y-0"
      leave-from-class="translate-y-0"
      leave-to-class="translate-y-full"
    >
      <div
        v-if="globalError"
        class="fixed bottom-4 right-4 bg-destructive text-destructive-foreground p-4 rounded-lg shadow-lg max-w-md z-40"
      >
        <div class="flex items-center gap-2">
          <AlertCircle class="h-4 w-4 flex-shrink-0" />
          <span class="text-sm">{{ globalError }}</span>
          <Button
            variant="ghost"
            size="sm"
            class="h-6 w-6 p-0 ml-auto hover:bg-destructive-foreground/20"
            @click="clearGlobalError"
          >
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { Loader2, AlertCircle, X } from 'lucide-vue-next';
import { useChat } from '@/composables/useChat';
import { useUI } from '@/composables/useUI';
import { useI18n } from '@/composables/useI18n';
import ChatSidebar from '@/components/ChatSidebar.vue';
import ChatInterface from '@/components/ChatInterface.vue';
import Button from '@/components/ui/Button.vue';

// Props (from router)
interface Props {
  id?: string;
}

const props = withDefaults(defineProps<Props>(), {
  id: undefined
});

// Router
const router = useRouter();

// Composables
const {
  selectedSession,
  selectedSessionId,
  selectSession,
  setMode,
  initialize,
  setSelectedSessionId,
  validateSessionExists,
  areSessionsLoaded,
  isLoading,
  error: chatError,
  clearError: clearChatError,
  chatState
} = useChat();

const { 
  isSidebarCollapsed, 
  toggleSidebar,
  cleanup: cleanupUI
} = useUI();

const { t } = useI18n();

// Local state
const mode = ref<'conversation' | 'document'>('conversation');
const isInitializing = ref(true);
const globalError = ref<string | null>(null);

// Computed
const hasSelectedSession = computed(() => !!selectedSessionId.value);

// Centralized route change handler (defined before watchers to avoid hoisting issues)
const handleRouteChange = async (sessionId: string | null) => {
  console.log('Index: handleRouteChange called with sessionId:', sessionId);
  console.log('Index: areSessionsLoaded():', areSessionsLoaded());
  console.log('Index: sessions count:', chatState.value.sessions.length);

  if (!sessionId) {
    // No ID means new session - show clean slate
    console.log('Index: No session ID, clearing selection');
    await setSelectedSessionId(null);
    return;
  }

  // If sessions aren't loaded yet, wait for them
  if (!areSessionsLoaded()) {
    console.log('Index: Sessions not loaded yet, waiting for session loading to complete');
    return;
  }

  // Validate session exists
  console.log('Index: Validating session exists:', sessionId);
  if (!validateSessionExists(sessionId)) {
    console.log('Index: Session not found in loaded sessions, redirecting to NotFound:', sessionId);
    console.log('Index: Available sessions:', chatState.value.sessions.map(s => s.id));
    router.replace({ name: 'NotFound' });
    return;
  }

  // Valid session ID - set it
  console.log('Index: Valid session found, selecting:', sessionId);
  await setSelectedSessionId(sessionId);
};

// Watch for route changes with session validation
watch(() => props.id, async (newId) => {
  console.log('Index: Route ID changed to:', newId);
  await handleRouteChange(newId);
}, { immediate: true });

// Watch for sessions loading completion to validate current route
watch(() => areSessionsLoaded(), async (loaded) => {
  if (loaded && props.id) {
    console.log('Index: Sessions loaded, re-validating current route:', props.id);
    await handleRouteChange(props.id);
  }
});

// Methods
const handleSelectSession = async (id: string) => {
  // Prevent selecting the same session or during loading
  if (selectedSessionId.value === id || isLoading.value) {
    console.log('Index: Skipping session selection - same ID or loading');
    return;
  }

  try {
    console.log('Index: Selecting session:', id);
    await selectSession(id);
  } catch (error: any) {
    console.error('Index: Failed to select session:', error);
    globalError.value = error.message || 'Failed to select session';
  }
};

const handleNewSession = async () => {
  console.log('Index: Starting new session (no ID)');

  // Clear selected session for new session
  await setSelectedSessionId(null);

  // Navigation is already handled by ChatSidebar component
  // This provides a clean slate for new sessions
};

const handleModeChange = (newMode: 'conversation' | 'document') => {
  mode.value = newMode;
  setMode(newMode);
};

const handleToggleSidebar = () => {
  toggleSidebar();
};

const handleSessionChange = (id: string) => {
  // Session change is handled by the global state
  console.log('Index: Session changed to:', id);
};

const handleHome = () => {
  // TODO: Implement home navigation
  console.log('Navigate to home');
};

const handleSearch = () => {
  // TODO: Implement global search
  console.log('Open global search');
};

const handleSettings = () => {
  // TODO: Implement settings
  console.log('Open settings');
};

const handleHelp = () => {
  // TODO: Implement help
  console.log('Open help');
};

const clearGlobalError = () => {
  globalError.value = null;
  clearChatError();
};

// Auto-clear errors after 5 seconds
const autoHideError = () => {
  if (globalError.value) {
    setTimeout(() => {
      globalError.value = null;
    }, 5000);
  }
};

// Watch for chat errors
const unwatchChatError = computed(() => {
  if (chatError.value && !globalError.value) {
    globalError.value = chatError.value;
    autoHideError();
  }
  return chatError.value;
});

// Watch for changes in selected session to keep state in sync
watch(selectedSession, (newSession) => {
  if (newSession) {
    console.log('Index: Selected session changed to:', newSession.id);
  } else {
    console.log('Index: No session selected');
  }
});

// Initialize the application
onMounted(async () => {
  try {
    console.log('Index: Initializing application...');
    await initialize();

    console.log('Index: Application initialized, sessions loaded:', areSessionsLoaded());

    // After initialization, handle the current route if there's an ID
    if (props.id) {
      console.log('Index: Application initialized with route ID:', props.id);
      await handleRouteChange(props.id);
    } else {
      console.log('Index: Application initialized without route ID');
    }
  } catch (error: any) {
    console.error('Index: Failed to initialize application:', error);
    globalError.value = error.message || 'Failed to initialize application';
  } finally {
    isInitializing.value = false;
  }
});

// Cleanup on unmount
onUnmounted(() => {
  cleanupUI();
});
</script>

<style scoped>
/* Transition styles */
.sidebar-enter-active,
.sidebar-leave-active {
  transition: transform 0.3s ease;
}

.sidebar-enter-from {
  transform: translateX(-100%);
}

.sidebar-leave-to {
  transform: translateX(-100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
