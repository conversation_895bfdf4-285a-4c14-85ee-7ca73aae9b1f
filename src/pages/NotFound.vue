<template>
  <div class="min-h-screen flex items-center justify-center bg-background">
    <div class="max-w-md w-full bg-card shadow-lg rounded-lg p-6 text-center border">
      <div class="mb-4">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-destructive/10">
          <AlertTriangle class="h-6 w-6 text-destructive" />
        </div>
      </div>
      <h1 class="text-2xl font-bold text-foreground mb-2">{{ t('errors.not_found_title') }}</h1>
      <p class="text-muted-foreground mb-6">
        {{ t('errors.not_found_message') }}
      </p>
      <Button
        @click="goHome"
        class="inline-flex items-center"
      >
        <Home class="h-4 w-4 mr-2" />
        {{ t('navigation.go_back_home') }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { AlertTriangle, Home } from 'lucide-vue-next';
import { useI18n } from '@/composables/useI18n';
import Button from '@/components/ui/Button.vue';

const router = useRouter();
const { t } = useI18n();

const goHome = () => {
  router.push('/');
};
</script>
