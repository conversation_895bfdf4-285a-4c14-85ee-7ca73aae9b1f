import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Vue-specific utility functions
export function formatTimestamp(timestamp: string, t?: (key: string, values?: any) => string): string {
  // If translation function is provided, use it for localized strings
  if (t) {
    if (timestamp === 'Now') {
      return t('time.now');
    }
    if (timestamp === 'Today') {
      return t('time.today');
    }

    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return t('time.just_now');
    } else if (diffInHours < 24) {
      return t('time.hours_ago', { count: Math.floor(diffInHours) });
    } else if (diffInHours < 48) {
      return t('time.yesterday');
    } else {
      return date.toLocaleDateString();
    }
  }

  // Fallback to English for backward compatibility
  if (timestamp === 'Now' || timestamp === 'Today') {
    return timestamp;
  }

  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h ago`;
  } else if (diffInHours < 48) {
    return 'Yesterday';
  } else {
    return date.toLocaleDateString();
  }
}

export function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
