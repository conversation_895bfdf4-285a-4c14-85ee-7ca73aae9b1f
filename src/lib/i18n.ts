import { createI18n } from 'vue-i18n'

// Import language files
import en from '@/locales/en.json'
import vi from '@/locales/vi.json'
import jp from '@/locales/jp.json'

// Supported locales
export enum LocaleEnum {
  EN = 'en',
  VI = 'vi',
  JP = 'jp'
}

// Locale configuration with metadata
export const localeMap = {
  [LocaleEnum.EN]: {
    key: LocaleEnum.EN,
    flag: '🇺🇸',
    name: 'English',
    language: 'English'
  },
  [LocaleEnum.VI]: {
    key: LocaleEnum.VI,
    flag: '🇻🇳',
    name: 'Tiếng Việt',
    language: 'Vietnamese'
  },
  [LocaleEnum.JP]: {
    key: LocaleEnum.JP,
    flag: '🇯🇵',
    name: '日本語',
    language: 'Japanese'
  }
}

// Default locale
export const DefaultLocale = LocaleEnum.EN

// Get saved locale from localStorage or use default
function getInitialLocale(): LocaleEnum {
  try {
    const saved = localStorage.getItem('locale')
    if (saved && Object.values(LocaleEnum).includes(saved as LocaleEnum)) {
      return saved as LocaleEnum
    }
  } catch (error) {
    console.warn('Failed to get locale from localStorage:', error)
  }
  return DefaultLocale
}

// Create i18n instance
export const i18n = createI18n({
  legacy: false, // Use Composition API mode
  locale: getInitialLocale(),
  fallbackLocale: DefaultLocale,
  globalInjection: true, // Enable global $t, $tc, etc.
  messages: {
    en,
    vi,
    jp
  },
  // Additional configuration
  silentTranslationWarn: false,
  silentFallbackWarn: false,
  formatFallbackMessages: true,
  warnHtmlMessage: true,
  escapeParameter: true
})

// Type definitions for better TypeScript support
export type MessageSchema = typeof en
export type LocaleKey = keyof MessageSchema

// Helper function to save locale to localStorage
export function saveLocale(locale: LocaleEnum): void {
  try {
    localStorage.setItem('locale', locale)
  } catch (error) {
    console.warn('Failed to save locale to localStorage:', error)
  }
}

// Helper function to get available locales
export function getAvailableLocales() {
  return Object.values(LocaleEnum).map(locale => ({
    ...localeMap[locale],
    value: locale
  }))
}

// Helper function to check if locale is supported
export function isSupportedLocale(locale: string): locale is LocaleEnum {
  return Object.values(LocaleEnum).includes(locale as LocaleEnum)
}

export default i18n
