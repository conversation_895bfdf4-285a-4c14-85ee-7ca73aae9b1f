// utils/markdown.ts
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import mathjax3Plugin from "@/utils/mathjax3-plugin";
import DOMPurify from 'dompurify';

export const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    highlight(code: string, lang: string): string {
        const valid = lang && hljs.getLanguage(lang);
        const html = valid
            ? hljs.highlight(code, { language: lang }).value
            : md.utils.escapeHtml(code);
        return buildCodeBlockWrapper(lang, `<pre><code class="hljs ${lang}">${html}</code></pre>`);
    },
})
    .use(mathjax3Plugin)
    .use(metadataPlugin);

function metadataPlugin(md: any) {
    const RE = /^\[\[([^|\]\n]+)\|([^\]\n]+)\]\]/;          // [[label|value]]

    md.inline.ruler.before('emphasis', 'pair', (state: any, silent: any) => {
        const m = RE.exec(state.src.slice(state.pos));
        if (!m) return false;
        if (silent) return true;

        const label = m[1].trim();
        const value = m[2].trim();

        const tok = state.push('c_metadata', '', 0);
        tok.meta = { label, value };

        state.pos += m[0].length;
        return true;
    });

    md.renderer.rules.c_metadata = (tokens: any, idx: any) => {
        const { label, value } = tokens[idx].meta;

        return `
            <span class="c_metadata" data-value="${value}">
              <span>[${md.utils.escapeHtml(label)}]</span>
            </span>`;
    };
}

md.renderer.rules.code_inline = (tokens, idx) => {
    const content = md.utils.escapeHtml(tokens[idx].content);
    return `<code class="inline-code">${content}</code>`;
};

const buildCodeBlockWrapper = (lang: string, htmlContent: any) => {
    const langDisplay = lang || 'text';
    return `
        <div class="hljs-code-wrapper">
            ${lang ? `<div class="hljs-code-wrapper--header">
                <span>${langDisplay}</span>
            </div>` : ''}
            ${htmlContent}
        </div>
    `;
}

// Safe markdown rendering function
export function renderMarkdown(content: string): string {
    try {
        const html = md.render(content);
        
        // Sanitize the HTML to prevent XSS attacks
        const sanitized = DOMPurify.sanitize(html, {
            ALLOWED_TAGS: [
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'p', 'br', 'div', 'span',
                'strong', 'b', 'em', 'i', 'u', 's', 'del',
                'ul', 'ol', 'li',
                'blockquote',
                'code', 'pre',
                'a',
                'table', 'thead', 'tbody', 'tr', 'th', 'td',
                'img',
                'hr',
                'svg', 'path', 'g', 'defs', 'use', 'rect', 'circle', 'ellipse', 'line', 'polyline', 'polygon'
            ],
            ALLOWED_ATTR: [
                'href', 'target', 'title', 'alt', 'src', 'width', 'height',
                'class', 'id', 'style', 'data-*',
                'viewBox', 'd', 'fill', 'stroke', 'stroke-width', 'transform',
                'x', 'y', 'x1', 'y1', 'x2', 'y2', 'cx', 'cy', 'r', 'rx', 'ry',
                'points', 'xmlns'
            ],
            ALLOW_DATA_ATTR: true
        });
        
        return sanitized;
    } catch (error) {
        console.error('Error rendering markdown:', error);
        return md.utils.escapeHtml(content);
    }
}

// Export the markdown instance for direct use if needed
export default md;
