import { createApp } from 'vue';
import { VueQueryPlugin } from '@tanstack/vue-query';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import 'remixicon/fonts/remixicon.css';
import 'highlight.js/styles/github-dark.css';
import App from './App.vue';
import router from './router';
import i18n from './lib/i18n';
import './index.css';

// Create Vue app instance
const app = createApp(App);

// Install plugins
app.use(router);
app.use(VueQueryPlugin);
app.use(ElementPlus);
app.use(i18n);

// Global error handler
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue error:', err);
  console.error('Component instance:', instance);
  console.error('Error info:', info);
};

// Global warning handler (development only)
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue warning:', msg);
    console.warn('Component trace:', trace);
  };
}

// Mount the app
app.mount('#root');
