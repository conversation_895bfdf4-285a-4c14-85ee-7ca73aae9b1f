<template>
  <div class="flex-1 flex h-full">
    <!-- Main Chat Area with Drag and Drop -->
    <div
      class="flex-1 flex flex-col h-full relative"
      @dragenter="handleDragEnter"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @drop="handleDrop"
    >
      <!-- Full-Screen Drag and Drop Overlay -->
      <div
        v-if="isDragging"
        class="absolute inset-0 bg-primary/10 backdrop-blur-sm border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-50"
      >
        <div class="text-center p-8">
          <div class="w-24 h-24 mx-auto bg-primary/20 rounded-2xl flex items-center justify-center mb-6">
            <i class="ri-upload-cloud-2-line text-4xl text-primary"></i>
          </div>
          <h3 class="text-xl font-semibold text-primary mb-2">{{ t('documents.drag_drop') }}</h3>
          <p class="text-muted-foreground mb-4">
            {{ t('chat.drag_drop_subtitle') }}
          </p>
          <div class="text-sm text-muted-foreground">
            {{ t('documents.supported_formats') }}
          </div>
        </div>
      </div>
      <!-- Header -->
      <ChatHeader 
        :title="headerTitle"
        :document-count="documentCount"
        :message-count="messageCount"
      />

      <!-- Messages Area -->
      <ScrollArea ref="scrollAreaRef" class="flex-1 px-6 py-4">
        <div class="max-w-4xl mx-auto">
          <!-- Welcome Message -->
          <div
            v-if="displayedMessages.length === 0 && !isLoading"
            class="text-center py-12"
          >
            <div class="mb-4">
              <div class="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <MessageSquare class="h-8 w-8 text-primary" />
              </div>
            </div>
            <h3 class="text-lg font-semibold mb-2">{{ t('chat.empty_state.title') }}</h3>
            <p class="text-muted-foreground mb-4">
              {{ t('chat.empty_state.subtitle') }}
            </p>
            <div class="flex flex-wrap gap-2 justify-center">
              <Button
                v-for="suggestion in suggestions"
                :key="suggestion"
                variant="outline"
                size="sm"
                @click="handleSuggestionClick(suggestion)"
              >
                {{ suggestion }}
              </Button>
            </div>
          </div>

          <!-- Messages -->
          <div v-else>
            <ChatMessage
              v-for="message in displayedMessages"
              :key="message.id"
              :message="message"
              @document-click="handleDocumentClick"
            />

            <!-- Loading Indicator -->
            <div
              v-if="isLoading"
              class="flex gap-3 mb-4"
            >
              <Avatar size="sm" class="bg-muted text-muted-foreground">
                <template #fallback>
                  <Bot class="h-3 w-3" />
                </template>
              </Avatar>
              <div class="flex-1">
                <div class="mb-1">
                  <span class="text-xs text-muted-foreground">{{ t('chat.ai_assistant') }}</span>
                </div>
                <div class="inline-block px-3 py-2 rounded-lg bg-background border border-border shadow-sm">
                  <div class="flex items-center gap-2">
                    <Loader2 class="h-4 w-4 animate-spin" />
                    <span class="text-sm text-muted-foreground">{{ t('chat.thinking') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div
            v-if="error"
            class="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg"
          >
            <div class="flex items-center gap-2">
              <AlertCircle class="h-4 w-4 text-destructive" />
              <span class="text-sm text-destructive">{{ error }}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              class="mt-2"
              @click="clearError"
            >
              {{ t('common.dismiss') }}
            </Button>
          </div>
        </div>
      </ScrollArea>

      <!-- Input -->
      <ChatInput
        ref="chatInputRef"
        :disabled="isLoading"
        :disable-drag-overlay="true"
        @send-message="handleSendMessage"
        @file-upload="handleFileUpload"
      />
    </div>

    <!-- Document Preview Panel -->
    <DocumentPreview
      v-if="selectedDocument"
      :document-name="selectedDocument"
      @close="handleCloseDocument"
      @download="handleDownloadDocument"
      @open-external="handleOpenExternal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { MessageSquare, Bot, Loader2, AlertCircle } from 'lucide-vue-next';
import type { ChatInterfaceProps } from '@/types';
import { useChat } from '@/composables/useChat';
import { useUI } from '@/composables/useUI';
import { useDocuments } from '@/composables/useDocuments';
import { useI18n } from '@/composables/useI18n';
import ChatHeader from '@/components/ChatHeader.vue';
import ChatMessage from '@/components/ChatMessage.vue';
import ChatInput from '@/components/ChatInput.vue';
import DocumentPreview from '@/components/DocumentPreview.vue';
import ScrollArea from '@/components/ui/ScrollArea.vue';
import Button from '@/components/ui/Button.vue';
import Avatar from '@/components/ui/Avatar.vue';

const props = defineProps<ChatInterfaceProps>();

const emit = defineEmits<{
  conversationChange: [id: string];  // sessionId change event
}>();

const {
  selectedSession,
  currentMessages,
  sendMessage,
  isLoading,
  error,
  clearError,
  clearLoadingState
} = useChat();

// Display messages directly from currentMessages (strict separation)
const displayedMessages = computed(() => {
  console.log('ChatInterface: Displaying', currentMessages.value.length, 'messages');
  return currentMessages.value;
});

const { selectedDocument, openDocumentPreview, closeDocumentPreview } = useUI();
const { uploadDocuments } = useDocuments();
const { t, getTranslationObject } = useI18n();

const scrollAreaRef = ref();
const chatInputRef = ref();

// Drag and drop state
const isDragging = ref(false);
const dragCounter = ref(0); // Track drag enter/leave events

// Computed properties
const headerTitle = computed(() =>
  selectedSession.value?.session_name || t('navigation.new_chat')
);

const documentCount = computed(() => {
  // Count documents from messages
  if (!displayedMessages.value) return 0;

  const documents = new Set();
  displayedMessages.value.forEach(message => {
    if (message.documents) {
      message.documents.forEach(doc => documents.add(doc.name));
    }
  });

  return documents.size;
});

const messageCount = computed(() =>
  displayedMessages.value.length
);

// Sample suggestions for empty state
const suggestions = computed(() => {
  const suggestionArray = getTranslationObject('chat.empty_state.suggestions');
  return Array.isArray(suggestionArray) ? suggestionArray : [];
});

// Methods
const handleSendMessage = async (content: string, files?: File[]) => {
  try {
    console.log('ChatInterface: Sending message with sessionId:', props.conversationId);

    // Upload files if provided
    if (files && files.length > 0 && props.conversationId) {
      await uploadDocuments(props.conversationId, files);
    }

    // Send message with the session ID from props (can be null for new sessions)
    await sendMessage(content, props.conversationId || null);

    // Scroll to bottom after message is sent
    await nextTick();
    scrollToBottom();
  } catch (error) {
    console.error('Failed to send message:', error);
  }
};

const handleFileUpload = async (files: File[]) => {
  // For new sessions (no ID), files will be handled when the first message is sent
  if (!props.conversationId) {
    console.log('Files uploaded for new session, will be processed with first message');
    return;
  }

  try {
    await uploadDocuments(props.conversationId, files);
  } catch (error) {
    console.error('Failed to upload files:', error);
  }
};

const handleDocumentClick = (documentName: string) => {
  openDocumentPreview(documentName);
};

const handleCloseDocument = () => {
  closeDocumentPreview();
};

const handleDownloadDocument = (documentName: string) => {
  // TODO: Implement document download
  console.log('Download document:', documentName);
};

const handleOpenExternal = (documentName: string) => {
  // TODO: Implement open in external viewer
  console.log('Open external:', documentName);
};

// Drag and drop handlers
const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();

  // Only track drag enter for files
  if (event.dataTransfer?.types.includes('Files')) {
    dragCounter.value++;
    isDragging.value = true;
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = 'copy';
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();

  // Use drag counter to properly handle nested elements
  dragCounter.value--;

  if (dragCounter.value <= 0) {
    isDragging.value = false;
    dragCounter.value = 0;
  }
};

const handleDrop = async (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
  dragCounter.value = 0;

  const files = Array.from(event.dataTransfer?.files || []);
  if (files.length > 0) {
    console.log('ChatInterface: Files dropped:', files.map(f => f.name));

    // Validate file types (same as ChatInput)
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ];

    const validFiles = files.filter(file =>
      allowedTypes.includes(file.type) ||
      /\.(pdf|doc|docx|txt|jpg|jpeg|png|gif)$/i.test(file.name)
    );

    if (validFiles.length !== files.length) {
      console.warn('Some files were filtered out due to unsupported types');
    }

    if (validFiles.length > 0) {
      // Add files to ChatInput component for display in file preview
      if (chatInputRef.value?.addFiles) {
        chatInputRef.value.addFiles(validFiles);
      }
    }
  }
};

const handleSuggestionClick = (suggestion: string) => {
  handleSendMessage(suggestion);
};

const scrollToBottom = () => {
  if (scrollAreaRef.value) {
    scrollAreaRef.value.scrollToBottom();
  }
};

// Watch for new messages and scroll to bottom
watch(displayedMessages, () => {
  nextTick(() => {
    scrollToBottom();
  });
}, { deep: true });

// Watch for session changes to emit events and handle loading state
watch(() => props.conversationId, (newId, oldId) => {
  console.log('ChatInterface: Session ID changed from', oldId, 'to', newId);
  console.log('ChatInterface: Current loading state:', isLoading.value);

  if (newId === null || newId === undefined) {
    // Clear any loading state for new sessions
    if (isLoading.value) {
      console.log('ChatInterface: Clearing loading state for new session');
      clearLoadingState();
    }
  } else {
    // Emit session change event
    console.log('ChatInterface: Emitting session change for', newId);
    emit('conversationChange', newId);
  }
}, { immediate: true });

// Watch for changes in selected session to track message updates
watch(selectedSession, (newSession) => {
  if (newSession) {
    console.log('ChatInterface: Selected session changed to:', newSession.id, 'with', currentMessages.value.length, 'messages');
  } else {
    console.log('ChatInterface: No session selected');
  }
}, { deep: true, immediate: true });

onMounted(() => {
  // Scroll to bottom on mount
  nextTick(() => {
    scrollToBottom();
    console.log(suggestions.value);
  });
});
</script>
