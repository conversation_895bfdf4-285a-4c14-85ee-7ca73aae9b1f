<template>
  <div class="language-selector">
    <el-tooltip :content="currentLocaleInfo.name" placement="bottom">
      <el-dropdown
        :trigger="trigger"
        placement="bottom-end"
        @command="handleLanguageChange"
        class="language-dropdown"
      >
        <el-button
          text
          circle
          size="small"
          :class="triggerClass"
        >
          <span class="flag">{{ currentLocaleInfo.flag }}</span>
        </el-button>

        <template #dropdown>
          <el-dropdown-menu class="language-menu">
            <el-dropdown-item
              v-for="locale in supportedLocales"
              :key="locale.value"
              :command="locale.value"
              :class="{ 'is-active': locale.value === currentLocale }"
              class="language-item"
            >
              <div class="language-option">
                <span class="flag">{{ locale.flag }}</span>
                <div class="language-info">
                  <span class="name">{{ locale.name }}</span>
                  <span v-if="showLanguageLabel" class="language">{{ locale.language }}</span>
                </div>
                <el-icon v-if="locale.value === currentLocale" class="check-icon">
                  <i class="ri-check-line"></i>
                </el-icon>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">

import { useI18n, type LocaleEnum } from '@/composables/useI18n'

interface LanguageSelectorProps {
  /** Trigger type for the dropdown */
  trigger?: 'hover' | 'click'
  /** Whether to show the language label next to the flag */
  showLabel?: boolean
  /** Whether to show the dropdown arrow */
  showArrow?: boolean
  /** Whether to show the language name in the dropdown items */
  showLanguageLabel?: boolean
  /** Size of the component */
  size?: 'small' | 'default' | 'large'
  /** Custom CSS class for the trigger */
  triggerClass?: string
  /** Whether the component is disabled */
  disabled?: boolean
}

const props = withDefaults(defineProps<LanguageSelectorProps>(), {
  trigger: 'click',
  showLabel: true,
  showArrow: true,
  showLanguageLabel: true,
  size: 'default',
  triggerClass: '',
  disabled: false
})

const emit = defineEmits<{
  /** Emitted when language changes */
  change: [locale: LocaleEnum, localeInfo: any]
}>()

// Use i18n composable
const {
  currentLocale,
  currentLocaleInfo,
  supportedLocales,
  changeLocale
} = useI18n()

// Handle language change
const handleLanguageChange = (locale: LocaleEnum) => {
  if (props.disabled || locale === currentLocale.value) {
    return
  }

  changeLocale(locale)

  // Emit change event
  try {
    const localeInfo = supportedLocales.value?.find((l: any) => l.value === locale)
    if (localeInfo) {
      emit('change', locale, localeInfo)
    }
  } catch (error) {
    console.warn('Error emitting language change event:', error)
  }
}


</script>

<style scoped>
.language-selector {
  display: inline-block;
}

.flag {
  font-size: 16px;
  line-height: 1;
  display: block;
}

/* Dropdown menu styles */
.language-menu {
  min-width: 200px;
  padding: 4px;
}

.language-item {
  padding: 0;
  margin: 2px 0;
  border-radius: 4px;
}

.language-item:hover {
  background: hsl(var(--muted));
}

.language-item.is-active {
  background: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  width: 100%;
}

.language-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 2px;
}

.name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.language {
  font-size: 12px;
  color: hsl(var(--muted-foreground));
  line-height: 1.2;
}

.check-icon {
  font-size: 16px;
  color: hsl(var(--primary));
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .language-trigger {
    padding: 6px 10px;
  }
  
  .language-trigger--no-label {
    padding: 6px;
  }
  
  .label {
    display: none;
  }
  
  .language-menu {
    min-width: 180px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .language-trigger {
    border-color: hsl(var(--border));
  }
  
  .language-trigger:hover:not(.language-trigger--disabled) {
    background: hsl(var(--muted));
  }
}

/* Focus styles for accessibility */
.language-trigger:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Animation for smooth transitions */
.language-selector * {
  transition: all 0.2s ease;
}
</style>
