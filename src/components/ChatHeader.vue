<template>
  <div class="border-b border-border bg-background h-[60px]">
    <!-- Header with navigation -->
    <div class="flex items-center justify-between pl-3 border-b border-border h-full">
      <div class="flex items-center gap-2 w-[800px]">
        <span class="text-sm font-medium truncate">{{ title }}</span>
      </div>      
      <div class="flex items-center justify-between gap-2 border-l h-full pr-1">
        <el-dropdown class="w-40 p-1" trigger="click" @command="handleCommand">
          <div class="w-full rounded p-3 flex justify-center hover:bg-[hsl(var(--muted))]">
            {{ selectedLabel }}
            <el-icon class="el-icon--right">
              <i class="ri-arrow-down-s-line"></i>
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="context-menu">
              <el-dropdown-item
                v-for="model in models"
                :key="model.value"
                :command="model.value"
                class="context-menu-item"
              >
                {{ model.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ChatHeaderProps } from '@/types';
import { computed, ref } from 'vue';

interface ModelOption {
  label: string;
  value: string;
}

const props = withDefaults(defineProps<ChatHeaderProps>(), {
  title: ''
});

const selectedModel = ref('gpt-4o');

const models: ModelOption[] = [
  { label: 'GPT-4', value: 'gpt-4' },
  { label: 'GPT-4o', value: 'gpt-4o' },
  { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
]

const selectedLabel = computed(() => {
  return models.find((m) => m.value === selectedModel.value)?.label || 'GPT-4o'
})

const handleCommand = (val: string) => {
  selectedModel.value = val
  console.log('Selected model:', val)
}
</script>

<style scoped>
.context-menu {
  background: hsl(var(--background));
  padding: 0.5rem 0;
  min-width: 152px;
  z-index: 10000;
}
</style>
