<template>
  <div class="w-80 border-l border-border bg-background flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-border">
      <div class="flex items-center gap-2 flex-1 min-w-0">
        <FileText class="h-4 w-4 text-primary flex-shrink-0" />
        <span class="text-sm font-medium truncate">{{ documentName }}</span>
      </div>
      <Button
        variant="ghost"
        size="sm"
        class="h-8 w-8 p-0 flex-shrink-0"
        @click="handleClose"
      >
        <X class="h-4 w-4" />
      </Button>
    </div>

    <!-- Document Content -->
    <ScrollArea class="flex-1 p-4">
      <div v-if="isLoading" class="flex items-center justify-center py-8">
        <Loader2 class="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
      
      <div v-else-if="error" class="text-center py-8">
        <AlertCircle class="h-8 w-8 mx-auto mb-2 text-destructive" />
        <p class="text-sm text-destructive">{{ error }}</p>
        <Button
          variant="outline"
          size="sm"
          class="mt-2"
          @click="loadContent"
        >
          {{ t('common.retry') }}
        </Button>
      </div>
      
      <div v-else class="space-y-4 text-sm">
        <!-- Work-Kinetic Energy PDF Content -->
        <template v-if="documentName === 'Work-Kinetic Energy.pdf'">
          <div class="bg-background border rounded-lg p-4 shadow-sm">
            <div class="text-xs text-muted-foreground mb-2">Page 2</div>
            <h4 class="font-semibold text-sm mb-2 text-primary">Work-Kinetic Energy Theorem</h4>
            <div class="text-xs space-y-2 leading-relaxed">
              <p><strong>Steps to Apply:</strong></p>
              <ol class="list-decimal list-inside space-y-1 ml-2">
                <li>Define the system</li>
                <li>Label the points between which work is done</li>
                <li>Draw the Free Body Diagram (FBD)</li>
                <li>Apply the work-energy principle (W=ΔE)</li>
                <li>Solve for the unknown</li>
              </ol>
              <div class="mt-3 p-2 bg-muted rounded text-xs">
                <strong>Key Formula:</strong> W<sub>net</sub> = ΔKE = ½mv₂² - ½mv₁²
              </div>
            </div>
          </div>
          
          <div class="bg-background border rounded-lg p-4 shadow-sm">
            <div class="text-xs text-muted-foreground mb-2">Page 3</div>
            <h4 class="font-semibold text-sm mb-2 text-primary">Example Problem</h4>
            <div class="text-xs space-y-2 leading-relaxed">
              <p>A 2 kg block slides down a frictionless incline...</p>
              <div class="mt-2 p-2 bg-muted rounded">
                <p><strong>Given:</strong> m = 2 kg, h = 3 m</p>
                <p><strong>Find:</strong> Final velocity at bottom</p>
              </div>
            </div>
          </div>
        </template>

        <!-- Potential Energy PDF Content -->
        <template v-else-if="documentName === 'Potential Energy Notes.pdf'">
          <div class="bg-background border rounded-lg p-4 shadow-sm">
            <div class="text-xs text-muted-foreground mb-2">Page 1</div>
            <h4 class="font-semibold text-sm mb-2 text-primary">Gravitational Potential Energy</h4>
            <div class="text-xs space-y-2 leading-relaxed">
              <p>Gravitational potential energy is the energy stored in an object due to its position in a gravitational field.</p>
              <div class="mt-2 p-2 bg-muted rounded">
                <strong>Formula:</strong> PE = mgh
              </div>
              <ul class="list-disc list-inside space-y-1 ml-2 mt-2">
                <li>m = mass (kg)</li>
                <li>g = gravitational acceleration (9.8 m/s²)</li>
                <li>h = height above reference point (m)</li>
              </ul>
            </div>
          </div>
          
          <div class="bg-background border rounded-lg p-4 shadow-sm">
            <div class="text-xs text-muted-foreground mb-2">Page 2</div>
            <h4 class="font-semibold text-sm mb-2 text-primary">Elastic Potential Energy</h4>
            <div class="text-xs space-y-2 leading-relaxed">
              <p>Energy stored in elastic materials when deformed.</p>
              <div class="mt-2 p-2 bg-muted rounded">
                <strong>Formula:</strong> PE = ½kx²
              </div>
            </div>
          </div>
        </template>

        <!-- Generic Document Content -->
        <template v-else>
          <div class="bg-muted/30 p-4 rounded-lg">
            <h3 class="font-medium mb-2">{{ t('documents.preview') }}</h3>
            <p class="text-muted-foreground text-xs">
              {{ t('documents.preview_description', { name: documentName }) }}
            </p>
          </div>
          
          <div class="space-y-2">
            <div class="bg-background border rounded p-3">
              <div class="text-xs text-muted-foreground mb-1">Page 1</div>
              <p class="text-xs">Sample content from the document...</p>
            </div>
            
            <div class="bg-background border rounded p-3">
              <div class="text-xs text-muted-foreground mb-1">Page 2</div>
              <p class="text-xs">More content with relevant information...</p>
            </div>
          </div>
        </template>
      </div>
    </ScrollArea>

    <!-- Footer Actions -->
    <div class="border-t border-border p-3">
      <div class="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          class="flex-1 text-xs"
          @click="downloadDocument"
        >
          <Download class="h-3 w-3 mr-1" />
          {{ t('common.download') }}
        </Button>
        <Button
          variant="outline"
          size="sm"
          class="flex-1 text-xs"
          @click="openInNewTab"
        >
          <ExternalLink class="h-3 w-3 mr-1" />
          {{ t('documents.open') }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { X, FileText, Loader2, AlertCircle, Download, ExternalLink } from 'lucide-vue-next';
import type { DocumentPreviewProps } from '@/types';
import { useDocuments } from '@/composables/useDocuments';
import { useI18n } from '@/composables/useI18n';
import Button from '@/components/ui/Button.vue';
import ScrollArea from '@/components/ui/ScrollArea.vue';

const props = defineProps<DocumentPreviewProps>();

const emit = defineEmits<{
  close: [];
  download: [documentName: string];
  openExternal: [documentName: string];
}>();

const { getDocumentContent } = useDocuments();
const { t } = useI18n();

const isLoading = ref(false);
const error = ref<string | null>(null);
const content = ref<any>(null);

const loadContent = async () => {
  try {
    isLoading.value = true;
    error.value = null;
    content.value = await getDocumentContent(props.documentName);
  } catch (err: any) {
    error.value = err.message || t('documents.load_error');
  } finally {
    isLoading.value = false;
  }
};

const handleClose = () => {
  emit('close');
};

const downloadDocument = () => {
  emit('download', props.documentName);
};

const openInNewTab = () => {
  emit('openExternal', props.documentName);
};

onMounted(() => {
  loadContent();
});
</script>
