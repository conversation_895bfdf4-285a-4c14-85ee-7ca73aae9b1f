<template>
  <div class="border-t border-border bg-background p-4">
    <form @submit.prevent="handleSubmit" class="max-w-4xl mx-auto">
      <div
        class="px-3 py-2"
        :class="inputContainerClass"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
      >
        <!-- File Upload Area (when dragging) - Only show if parent doesn't handle drag -->
        <div
          v-if="isDragging && !props.disableDragOverlay"
          class="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-10"
        >
          <div class="text-center">
            <i class="ri-upload-cloud-2-line text-3xl text-primary mb-2"></i>
            <p class="text-sm font-medium text-primary">{{ t('documents.drag_drop') }}</p>
            <p class="text-xs text-muted-foreground">{{ t('documents.supported_formats') }}</p>
          </div>
        </div>

        <!-- Text Input Area -->
        <div>
          <el-input
            ref="textareaRef"
            v-model="message"
            type="textarea"
            :placeholder="placeholder || t('chat.placeholder')"
            :disabled="disabled"
            :autosize="{ minRows: 2, maxRows: 8 }"
            resize="none"
            class="chat-input"
            @keydown="handleKeyDown"
          />
        </div>
        <!-- Hidden File Input -->
        <input
          ref="fileInputRef"
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
          class="hidden"
          @change="handleFileUpload"
        />

        <!-- Upload Progress -->
        <div
          v-if="uploadProgress > 0 && uploadProgress < 100"
          class="mb-3"
        >
          <div class="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <i class="ri-loader-4-line animate-spin"></i>
            <span>Uploading files... {{ uploadProgress }}%</span>
          </div>
          <el-progress
            :percentage="uploadProgress"
            :show-text="false"
            :stroke-width="6"
            class="upload-progress"
          />
        </div>

        <!-- File Preview -->
        <div
          v-if="selectedFiles.length > 0"
          class="mb-3"
        >
          <div class="flex flex-wrap gap-2">
            <el-tag
              v-for="(file, index) in selectedFiles"
              :key="index"
              closable
              type="info"
              size="default"
              @close="removeFile(index)"
              class="file-tag"
            >
              <div class="flex items-center gap-1">
                <i class="ri-file-text-line text-sm"></i>
                <span class="truncate max-w-32">{{ file.name }}</span>
              </div>
            </el-tag>
          </div>
        </div>

        <!-- Action Buttons Row -->
        <div class="flex items-center justify-between">
          <!-- Left Side: Attachment Button -->
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-2">
              <el-button
                type="default"
                :icon="PaperclipIcon"
                circle
                size="default"
                @click="triggerFileUpload"
                :disabled="disabled"
                class="attachment-button"
                title="Attach files"
              />
              <span v-if="selectedFiles.length > 0" class="text-xs text-muted-foreground">
                {{ selectedFiles.length }} file{{ selectedFiles.length > 1 ? 's' : '' }} selected
              </span>
            </div>

            <!-- Center: Collection Name -->
            <div class="flex items-center gap-1 collection-display">
              <span class="text-xs text-muted-foreground">Collection:</span>
              <div v-if="!isEditingCollection" class="collection-name-display">
                <span
                  class="text-sm font-medium text-foreground cursor-pointer hover:text-primary transition-colors collection-name-text"
                  @click="startEditingCollection"
                  :title="`Click to edit collection: ${collectionName}`"
                >
                  {{ collectionName }}
                </span>
                <i class="ri-edit-2-line text-xs text-muted-foreground ml-1 opacity-60"></i>
              </div>
              <input
                v-else
                v-model="collectionEditValue"
                class="collection-edit-input"
                @blur="confirmCollectionEdit"
                @keyup.enter="confirmCollectionEdit"
                @keyup.esc="cancelCollectionEdit"
                @click.stop
                maxlength="50"
              />
            </div>
          </div>
          
          <!-- Right Side: Send Button -->
          <div class="flex items-center gap-2">
            <el-button
              type="primary"
              :icon="SendIcon"
              circle
              :disabled="!canSend"
              :loading="isSubmitting"
              @click="handleSubmit"
              class="send-button"
            >
            </el-button>
          </div>
        </div>        
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { Paperclip as PaperclipIcon, Promotion as SendIcon } from '@element-plus/icons-vue';
import { useI18n } from '@/composables/useI18n';
import type { ChatInputProps } from '@/types';

const { t } = useI18n();

const props = withDefaults(defineProps<ChatInputProps>(), {
  placeholder: '',
  disabled: false
});

const emit = defineEmits<{
  sendMessage: [content: string, files?: File[]];
  fileUpload: [files: File[]];
  collectionChange: [name: string];
}>();

const message = ref('');
const isDragging = ref(false);
const selectedFiles = ref<File[]>([]);
const uploadProgress = ref(0);
const fileInputRef = ref<HTMLInputElement>();
const textareaRef = ref();
const isSubmitting = ref(false);

// Collection name state
const collectionName = ref('General');
const isEditingCollection = ref(false);
const collectionEditValue = ref('');

const canSend = computed(() =>
  !props.disabled && !isSubmitting.value && (message.value.trim().length > 0 || selectedFiles.value.length > 0)
);

const inputContainerClass = computed(() => [
  'relative rounded-lg border border-input bg-background transition-colors',
  isDragging.value && 'border-primary bg-primary/5',
  'focus-within:border-ring focus-within:ring-1 focus-within:ring-ring'
]);

const handleSubmit = async () => {
  if (!canSend.value || isSubmitting.value) return;

  try {
    isSubmitting.value = true;

    const content = message.value.trim();
    const files = [...selectedFiles.value];

    if (content || files.length > 0) {
      emit('sendMessage', content, files.length > 0 ? files : undefined);

      // Clear input
      message.value = '';
      selectedFiles.value = [];

      // Reset textarea height
      await nextTick();
      if (textareaRef.value?.textareaRef) {
        textareaRef.value.textareaRef.style.height = 'auto';
      }
    }
  } finally {
    // Add a small delay to prevent rapid clicking
    setTimeout(() => {
      isSubmitting.value = false;
    }, 500);
  }
};

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    handleSubmit();
  }
};

// Remove unused function

const triggerFileUpload = () => {
  fileInputRef.value?.click();
};

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  
  if (files.length > 0) {
    selectedFiles.value.push(...files);
    emit('fileUpload', files);
  }
  
  // Reset input
  if (target) target.value = '';
};

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1);
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = 'copy';
  isDragging.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  // Only hide if leaving the container entirely
  const target = event.currentTarget as HTMLElement;
  const relatedTarget = event.relatedTarget as Node;
  if (!target?.contains(relatedTarget)) {
    isDragging.value = false;
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
  
  const files = Array.from(event.dataTransfer?.files || []);
  if (files.length > 0) {
    selectedFiles.value.push(...files);
    emit('fileUpload', files);
  }
};

// Method to add files from parent component (for expanded drag-and-drop)
const addFiles = (files: File[]) => {
  selectedFiles.value.push(...files);
  emit('fileUpload', files);
};

// Collection name editing functions
const startEditingCollection = () => {
  isEditingCollection.value = true;
  collectionEditValue.value = collectionName.value;

  // Focus the input after Vue updates the DOM
  nextTick(() => {
    const input = document.querySelector('.collection-edit-input') as HTMLInputElement;
    if (input) {
      input.focus();
      input.select();
    }
  });
};

const confirmCollectionEdit = () => {
  if (collectionEditValue.value.trim()) {
    collectionName.value = collectionEditValue.value.trim();
    emit('collectionChange', collectionName.value);
  }
  cancelCollectionEdit();
};

const cancelCollectionEdit = () => {
  isEditingCollection.value = false;
  collectionEditValue.value = '';
};

// Expose methods for parent components
defineExpose({
  focus: () => textareaRef.value?.focus(),
  clear: () => {
    message.value = '';
    selectedFiles.value = [];
  },
  addFiles
});
</script>

<style scoped>
.chat-input :deep(.el-textarea__inner) {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  resize: none !important;
  padding: 0.5rem 0rem !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
}

.chat-input :deep(.el-input__wrapper) {
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.75rem !important;
  background: hsl(var(--background)) !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
}

.chat-input :deep(.el-input__wrapper:hover) {
  border-color: hsl(var(--ring)) !important;
}

.chat-input :deep(.el-input__wrapper.is-focus) {
  border-color: hsl(var(--ring)) !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2) !important;
}

.attachment-button {
  transition: all 0.2s ease;
}

.attachment-button:hover {
  background: hsl(var(--muted));
  border-color: hsl(var(--ring));
}

.send-button {
  font-weight: 500;
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* File Preview Styles */
.file-tag {
  max-width: 200px;
  transition: all 0.2s ease;
}

.file-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-tag .truncate {
  max-width: 8rem;
}

/* Upload Progress Styles */
.upload-progress :deep(.el-progress-bar__outer) {
  background: hsl(var(--muted));
  border-radius: 0.25rem;
}

.upload-progress :deep(.el-progress-bar__inner) {
  background: hsl(var(--primary));
  border-radius: 0.25rem;
}

/* Collection Name Display Styles */
.collection-display {
  flex: 1;
  justify-content: center;
  max-width: 200px;
  margin: 0 1rem;
}

.collection-name-display {
  display: flex;
  align-items: center;
}

.collection-name-text {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.collection-name-text:hover + i {
  opacity: 1;
}

.collection-edit-input {
  background: hsl(var(--background));
  border: 1px solid hsl(var(--primary));
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
  transition: all 0.2s ease;
  width: 120px;
}

.collection-edit-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.3);
}

/* Responsive Design */
@media (max-width: 640px) {
  .collection-display {
    max-width: 150px;
    margin: 0 0.5rem;
  }

  .collection-name-text {
    max-width: 80px;
    font-size: 0.75rem;
  }

  .collection-edit-input {
    width: 80px;
    font-size: 0.75rem;
    padding: 0.125rem 0.25rem;
  }

  .send-button {
    font-size: 0.875rem;
  }

  .file-tag .truncate {
    max-width: 6rem;
  }
}
</style>
