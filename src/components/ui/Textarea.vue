<template>
  <textarea
    ref="textareaRef"
    :class="textareaClass"
    :placeholder="placeholder"
    :disabled="disabled"
    :rows="rows"
    :value="modelValue"
    v-bind="$attrs"
    @input="handleInput"
    @focus="handleFocus"
    @blur="handleBlur"
    @keydown="handleKeydown"
  />
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import { cn } from '@/lib/utils';

interface TextareaProps {
  modelValue?: string;
  placeholder?: string;
  disabled?: boolean;
  rows?: number;
  autoResize?: boolean;
  maxRows?: number;
  class?: string;
}

const props = withDefaults(defineProps<TextareaProps>(), {
  rows: 3,
  disabled: false,
  autoResize: false,
  maxRows: 10
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  focus: [event: FocusEvent];
  blur: [event: FocusEvent];
  keydown: [event: KeyboardEvent];
  enter: [event: KeyboardEvent];
  'shift-enter': [event: KeyboardEvent];
}>();

const textareaRef = ref<HTMLTextAreaElement>();

const textareaClass = computed(() =>
  cn(
    "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none",
    props.class
  )
);

const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  emit('update:modelValue', target.value);
  
  if (props.autoResize) {
    autoResize();
  }
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event);
  
  if (event.key === 'Enter') {
    if (event.shiftKey) {
      emit('shift-enter', event);
    } else {
      emit('enter', event);
    }
  }
};

const autoResize = async () => {
  if (!textareaRef.value || !props.autoResize) return;
  
  await nextTick();
  
  const textarea = textareaRef.value;
  textarea.style.height = 'auto';
  
  const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
  const maxHeight = lineHeight * props.maxRows;
  const newHeight = Math.min(textarea.scrollHeight, maxHeight);
  
  textarea.style.height = `${newHeight}px`;
};

const focus = () => {
  textareaRef.value?.focus();
};

const blur = () => {
  textareaRef.value?.blur();
};

const select = () => {
  textareaRef.value?.select();
};

// Watch for value changes to trigger auto-resize
watch(() => props.modelValue, () => {
  if (props.autoResize) {
    nextTick(() => autoResize());
  }
});

defineExpose({
  focus,
  blur,
  select,
  textareaRef
});
</script>
