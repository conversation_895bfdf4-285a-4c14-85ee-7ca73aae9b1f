<template>
  <div :class="avatarClass">
    <img
      v-if="src && !imageError"
      :src="src"
      :alt="alt"
      :class="imageClass"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    <div
      v-else
      :class="fallbackClass"
    >
      <slot name="fallback">
        {{ fallbackText }}
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { cn } from '@/lib/utils';

interface AvatarProps {
  src?: string;
  alt?: string;
  fallback?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  class?: string;
}

const props = withDefaults(defineProps<AvatarProps>(), {
  size: 'md',
  alt: 'Avatar'
});

const imageError = ref(false);
const imageLoaded = ref(false);

const avatarClass = computed(() =>
  cn(
    "relative flex shrink-0 overflow-hidden rounded-full",
    {
      'h-6 w-6': props.size === 'sm',
      'h-8 w-8': props.size === 'md', 
      'h-12 w-12': props.size === 'lg',
      'h-16 w-16': props.size === 'xl'
    },
    props.class
  )
);

const imageClass = computed(() =>
  cn(
    "aspect-square h-full w-full object-cover",
    !imageLoaded.value && "opacity-0",
    imageLoaded.value && "opacity-100 transition-opacity duration-200"
  )
);

const fallbackClass = computed(() =>
  cn(
    "flex h-full w-full items-center justify-center rounded-full bg-muted text-muted-foreground",
    {
      'text-xs': props.size === 'sm',
      'text-sm': props.size === 'md',
      'text-base': props.size === 'lg', 
      'text-lg': props.size === 'xl'
    }
  )
);

const fallbackText = computed(() => {
  if (props.fallback) return props.fallback;
  if (props.alt) {
    return props.alt
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }
  return '?';
});

const handleImageError = () => {
  imageError.value = true;
  imageLoaded.value = false;
};

const handleImageLoad = () => {
  imageError.value = false;
  imageLoaded.value = true;
};
</script>
