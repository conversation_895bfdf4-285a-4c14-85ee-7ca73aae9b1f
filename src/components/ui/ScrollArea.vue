<template>
  <div
    ref="scrollAreaRef"
    :class="scrollAreaClass"
    @scroll="handleScroll"
  >
    <div
      ref="viewportRef"
      :class="viewportClass"
      :style="viewportStyle"
    >
      <slot />
    </div>
    
    <!-- Vertical Scrollbar -->
    <div
      v-if="showScrollbar && scrollbarHeight < 100"
      :class="scrollbarClass"
      :style="scrollbarStyle"
    >
      <div
        :class="thumbClass"
        :style="thumbStyle"
        @mousedown="startDrag"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { cn } from '@/lib/utils';

interface ScrollAreaProps {
  class?: string;
  orientation?: 'vertical' | 'horizontal' | 'both';
  type?: 'auto' | 'always' | 'scroll' | 'hover';
}

const props = withDefaults(defineProps<ScrollAreaProps>(), {
  orientation: 'vertical',
  type: 'auto'
});

const emit = defineEmits<{
  scroll: [event: Event];
}>();

const scrollAreaRef = ref<HTMLElement>();
const viewportRef = ref<HTMLElement>();
const scrollTop = ref(0);
const scrollHeight = ref(0);
const clientHeight = ref(0);
const isDragging = ref(false);
const showScrollbar = ref(false);

const scrollAreaClass = computed(() =>
  cn(
    "relative overflow-hidden",
    props.class
  )
);

const viewportClass = computed(() =>
  cn(
    "h-full w-full rounded-[inherit]",
    props.orientation === 'vertical' && "overflow-y-auto overflow-x-hidden",
    props.orientation === 'horizontal' && "overflow-x-auto overflow-y-hidden",
    props.orientation === 'both' && "overflow-auto"
  )
);

const viewportStyle = computed(() => ({
  scrollbarWidth: 'none',
  msOverflowStyle: 'none',
  '&::-webkit-scrollbar': {
    display: 'none'
  }
}));

const scrollbarHeight = computed(() => {
  if (scrollHeight.value === 0) return 100;
  return (clientHeight.value / scrollHeight.value) * 100;
});

const thumbTop = computed(() => {
  if (scrollHeight.value === 0) return 0;
  return (scrollTop.value / (scrollHeight.value - clientHeight.value)) * (100 - scrollbarHeight.value);
});

const scrollbarClass = computed(() =>
  cn(
    "absolute right-0 top-0 z-10 h-full w-2.5 border-l border-l-transparent p-[1px]",
    props.type === 'hover' && "opacity-0 transition-opacity hover:opacity-100",
    showScrollbar.value && "opacity-100"
  )
);

const scrollbarStyle = computed(() => ({
  display: scrollbarHeight.value >= 100 ? 'none' : 'block'
}));

const thumbClass = computed(() =>
  cn(
    "relative flex-1 rounded-full bg-border cursor-pointer",
    isDragging.value && "bg-border/80"
  )
);

const thumbStyle = computed(() => ({
  height: `${scrollbarHeight.value}%`,
  top: `${thumbTop.value}%`,
  position: 'absolute' as const,
  width: '100%'
}));

const updateScrollInfo = () => {
  if (!viewportRef.value) return;
  
  scrollTop.value = viewportRef.value.scrollTop;
  scrollHeight.value = viewportRef.value.scrollHeight;
  clientHeight.value = viewportRef.value.clientHeight;
  showScrollbar.value = scrollHeight.value > clientHeight.value;
};

const handleScroll = (event: Event) => {
  updateScrollInfo();
  emit('scroll', event);
};

const startDrag = (event: MouseEvent) => {
  event.preventDefault();
  isDragging.value = true;
  
  const startY = event.clientY;
  const startScrollTop = scrollTop.value;
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!viewportRef.value) return;
    
    const deltaY = e.clientY - startY;
    const scrollRatio = deltaY / (clientHeight.value - (scrollbarHeight.value / 100) * clientHeight.value);
    const newScrollTop = startScrollTop + scrollRatio * (scrollHeight.value - clientHeight.value);
    
    viewportRef.value.scrollTop = Math.max(0, Math.min(newScrollTop, scrollHeight.value - clientHeight.value));
  };
  
  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
  
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const scrollToBottom = () => {
  if (!viewportRef.value) return;
  viewportRef.value.scrollTop = viewportRef.value.scrollHeight;
};

const scrollToTop = () => {
  if (!viewportRef.value) return;
  viewportRef.value.scrollTop = 0;
};

onMounted(() => {
  nextTick(() => {
    updateScrollInfo();
  });
});

defineExpose({
  scrollToBottom,
  scrollToTop,
  scrollAreaRef,
  viewportRef
});
</script>

<style scoped>
.scroll-area::-webkit-scrollbar {
  display: none;
}

.scroll-area {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
</style>
