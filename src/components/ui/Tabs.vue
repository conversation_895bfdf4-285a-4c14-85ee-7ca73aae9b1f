<template>
  <div :class="tabsClass">
    <div
      :class="tabsListClass"
      role="tablist"
    >
      <button
        v-for="tab in tabs"
        :key="tab.value"
        :class="getTabTriggerClass(tab.value)"
        :aria-selected="modelValue === tab.value"
        role="tab"
        @click="selectTab(tab.value)"
      >
        {{ tab.label }}
      </button>
    </div>
    
    <div
      v-for="tab in tabs"
      :key="`content-${tab.value}`"
      v-show="modelValue === tab.value"
      :class="tabsContentClass"
      role="tabpanel"
    >
      <slot :name="tab.value" :tab="tab">
        {{ tab.content }}
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { cn } from '@/lib/utils';

interface Tab {
  value: string;
  label: string;
  content?: string;
  disabled?: boolean;
}

interface TabsProps {
  modelValue: string;
  tabs: Tab[];
  orientation?: 'horizontal' | 'vertical';
  class?: string;
}

const props = withDefaults(defineProps<TabsProps>(), {
  orientation: 'horizontal'
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
}>();

const tabsClass = computed(() =>
  cn(
    "w-full",
    props.orientation === 'vertical' && "flex gap-4",
    props.class
  )
);

const tabsListClass = computed(() =>
  cn(
    "inline-flex items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
    props.orientation === 'horizontal' && "h-10 w-full",
    props.orientation === 'vertical' && "flex-col h-auto w-auto"
  )
);

const tabsContentClass = computed(() =>
  cn(
    "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
    props.orientation === 'vertical' && "flex-1 mt-0"
  )
);

const getTabTriggerClass = (value: string) => {
  const isActive = props.modelValue === value;
  const tab = props.tabs.find(t => t.value === value);
  
  return cn(
    "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
    isActive && "bg-background text-foreground shadow-sm",
    !isActive && "hover:bg-background/50",
    tab?.disabled && "opacity-50 cursor-not-allowed",
    props.orientation === 'vertical' && "w-full justify-start"
  );
};

const selectTab = (value: string) => {
  const tab = props.tabs.find(t => t.value === value);
  if (tab?.disabled) return;
  
  emit('update:modelValue', value);
  emit('change', value);
};
</script>
