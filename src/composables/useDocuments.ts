import { ref, computed } from 'vue';
import type { Document } from '@/types';
import { useApi } from '@/composables/useApi';

// Global document state
const documentState = ref({
  documents: [] as Document[],
  selectedDocument: null as Document | null,
  documentContent: {} as Record<string, any>,
  isLoading: false,
  error: null as string | null,
  uploadProgress: 0
});

export function useDocuments() {
  // Initialize the API composable
  const api = useApi();

  // Computed properties
  const documents = computed(() => documentState.value.documents);
  const selectedDocument = computed(() => documentState.value.selectedDocument);
  const isLoading = computed(() => documentState.value.isLoading);
  const error = computed(() => documentState.value.error);
  const uploadProgress = computed(() => documentState.value.uploadProgress);

  // Actions
  const uploadDocuments = async (conversationId: string, files: File[]) => {
    try {
      documentState.value.isLoading = true;
      documentState.value.error = null;
      documentState.value.uploadProgress = 0;

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        if (documentState.value.uploadProgress < 90) {
          documentState.value.uploadProgress += 10;
        }
      }, 150);

      const response = await api.uploadDocuments({ conversationId, files });
      
      clearInterval(progressInterval);
      documentState.value.uploadProgress = 100;
      
      // Add uploaded documents to state
      documentState.value.documents.push(...response.data.documents);
      
      // Reset progress after a short delay
      setTimeout(() => {
        documentState.value.uploadProgress = 0;
      }, 1000);

      return response.data.documents;
    } catch (error: any) {
      documentState.value.error = error.message || 'Failed to upload documents';
      console.error('Error uploading documents:', error);
      throw error;
    } finally {
      documentState.value.isLoading = false;
    }
  };

  const getDocumentContent = async (documentName: string) => {
    try {
      documentState.value.isLoading = true;
      documentState.value.error = null;

      // Check if content is already cached
      if (documentState.value.documentContent[documentName]) {
        return documentState.value.documentContent[documentName];
      }

      const response = await api.getDocumentContent(documentName);
      
      // Cache the content
      documentState.value.documentContent[documentName] = response.data;
      
      return response.data;
    } catch (error: any) {
      documentState.value.error = error.message || 'Failed to load document content';
      console.error('Error loading document content:', error);
      throw error;
    } finally {
      documentState.value.isLoading = false;
    }
  };

  const selectDocument = (document: Document) => {
    documentState.value.selectedDocument = document;
  };

  const clearSelectedDocument = () => {
    documentState.value.selectedDocument = null;
  };

  const removeDocument = (documentName: string) => {
    documentState.value.documents = documentState.value.documents.filter(
      doc => doc.name !== documentName
    );
    
    // Clear cached content
    delete documentState.value.documentContent[documentName];
    
    // Clear selection if this document was selected
    if (documentState.value.selectedDocument?.name === documentName) {
      documentState.value.selectedDocument = null;
    }
  };

  const getDocumentsByType = (type: Document['type']) => {
    return documentState.value.documents.filter(doc => doc.type === type);
  };

  const searchDocuments = (query: string) => {
    const lowerQuery = query.toLowerCase();
    return documentState.value.documents.filter(doc =>
      doc.name.toLowerCase().includes(lowerQuery) ||
      doc.content?.toLowerCase().includes(lowerQuery)
    );
  };

  const getDocumentSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 10MB' };
    }

    // Check file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'File type not supported' };
    }

    return { valid: true };
  };

  const clearError = () => {
    documentState.value.error = null;
  };

  const resetUploadProgress = () => {
    documentState.value.uploadProgress = 0;
  };

  // File drag and drop helpers
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    event.dataTransfer!.dropEffect = 'copy';
  };

  const handleDrop = async (event: DragEvent, conversationId: string) => {
    event.preventDefault();
    
    const files = Array.from(event.dataTransfer?.files || []);
    if (files.length === 0) return;

    // Validate files
    const validFiles: File[] = [];
    const errors: string[] = [];

    files.forEach(file => {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      documentState.value.error = errors.join(', ');
    }

    if (validFiles.length > 0) {
      await uploadDocuments(conversationId, validFiles);
    }
  };

  return {
    // State
    documents,
    selectedDocument,
    isLoading,
    error,
    uploadProgress,
    documentContent: computed(() => documentState.value.documentContent),
    
    // Actions
    uploadDocuments,
    getDocumentContent,
    selectDocument,
    clearSelectedDocument,
    removeDocument,
    getDocumentsByType,
    searchDocuments,
    getDocumentSize,
    validateFile,
    clearError,
    resetUploadProgress,
    
    // Drag and drop helpers
    handleDragOver,
    handleDrop
  };
}
