import { computed, watch } from 'vue'
import { useI18n as useVueI18n } from 'vue-i18n'
import { 
  LocaleEnum, 
  localeMap, 
  DefaultLocale, 
  saveLocale, 
  getAvailableLocales,
  isSupportedLocale,
  type MessageSchema,
  type LocaleKey
} from '@/lib/i18n'

/**
 * Custom i18n composable that wraps Vue i18n with additional functionality
 * Provides language switching, persistence, and type-safe translations
 */
export function useI18n() {
  // Get Vue i18n instance
  const { locale, t, te, tm, rt, d, n, ...vueI18n } = useVueI18n<MessageSchema>()

  // Current locale as computed property
  const currentLocale = computed({
    get: () => locale.value as LocaleEnum,
    set: (newLocale: LocaleEnum) => {
      if (isSupportedLocale(newLocale)) {
        locale.value = newLocale
        saveLocale(newLocale)
      }
    }
  })

  // Get current locale metadata
  const currentLocaleInfo = computed(() => localeMap[currentLocale.value])

  // Available locales for language selector
  const supportedLocales = computed(() => getAvailableLocales())

  // Check if current locale is RTL (none of our supported languages are RTL, but good to have)
  const isRTL = computed(() => false) // Can be extended for RTL languages

  /**
   * Change the current locale
   * @param newLocale - The locale to switch to
   */
  const changeLocale = (newLocale: LocaleEnum) => {
    if (isSupportedLocale(newLocale) && newLocale !== currentLocale.value) {
      currentLocale.value = newLocale
      console.log(`Language changed to: ${localeMap[newLocale].name}`)
    }
  }

  /**
   * Get next locale in the list (useful for cycling through languages)
   */
  const getNextLocale = (): LocaleEnum => {
    const locales = Object.values(LocaleEnum)
    const currentIndex = locales.indexOf(currentLocale.value)
    const nextIndex = (currentIndex + 1) % locales.length
    return locales[nextIndex]
  }

  /**
   * Cycle to the next available locale
   */
  const cycleLocale = () => {
    changeLocale(getNextLocale())
  }

  /**
   * Reset to default locale
   */
  const resetToDefault = () => {
    changeLocale(DefaultLocale)
  }

  /**
   * Type-safe translation function with better error handling
   * @param key - Translation key
   * @param values - Interpolation values
   * @param options - Translation options
   */
  const translate = (key: string, values?: Record<string, any>, options?: any) => {
    try {
      // Check if translation exists
      if (!te(key)) {
        console.warn(`Translation key not found: ${key}`)
        return key // Return the key itself as fallback
      }
      return t(key, values, options)
    } catch (error) {
      console.error(`Translation error for key "${key}":`, error)
      return key
    }
  }

  /**
   * Pluralization helper
   * @param key - Translation key
   * @param count - Number for pluralization
   * @param values - Additional interpolation values
   */
  const translatePlural = (key: string, count: number, values?: Record<string, any>) => {
    return translate(key, { count, ...values })
  }

  /**
   * Check if a translation key exists
   * @param key - Translation key to check
   */
  const hasTranslation = (key: string): boolean => {
    return te(key)
  }

  /**
   * Get raw translation object (useful for complex nested translations)
   * @param key - Translation key
   */
  const getTranslationObject = (key: string) => {
    try {
      return tm(key)
    } catch (error) {
      console.error(`Error getting translation object for key "${key}":`, error)
      return null
    }
  }

  /**
   * Format date according to current locale
   * @param date - Date to format
   * @param format - Date format options
   */
  const formatDate = (date: Date | string | number, format?: Intl.DateTimeFormatOptions) => {
    try {
      return d(new Date(date), format)
    } catch (error) {
      console.error('Date formatting error:', error)
      return new Date(date).toLocaleDateString()
    }
  }

  /**
   * Format number according to current locale
   * @param number - Number to format
   * @param format - Number format options
   */
  const formatNumber = (number: number, format?: Intl.NumberFormatOptions) => {
    try {
      return n(number, format)
    } catch (error) {
      console.error('Number formatting error:', error)
      return number.toString()
    }
  }

  // Watch for locale changes and perform any necessary side effects
  watch(currentLocale, (newLocale, oldLocale) => {
    if (newLocale !== oldLocale) {
      // Update document language attribute
      if (typeof document !== 'undefined') {
        document.documentElement.lang = newLocale
      }
      
      // Update document direction (for future RTL support)
      if (typeof document !== 'undefined') {
        document.documentElement.dir = isRTL.value ? 'rtl' : 'ltr'
      }
      
      // Emit custom event for other parts of the app to listen to
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('locale-changed', { 
          detail: { newLocale, oldLocale } 
        }))
      }
    }
  }, { immediate: true })

  return {
    // State
    currentLocale,
    currentLocaleInfo,
    supportedLocales,
    isRTL,
    
    // Actions
    changeLocale,
    cycleLocale,
    resetToDefault,
    
    // Translation functions
    t: translate,
    translate,
    translatePlural,
    hasTranslation,
    getTranslationObject,
    
    // Formatting functions
    formatDate,
    formatNumber,
    
    // Utilities
    getNextLocale,
    
    // Vue i18n instance (for advanced usage)
    ...vueI18n
  }
}

// Export types for better TypeScript support
export type { LocaleEnum, MessageSchema, LocaleKey }

// Export locale map for external usage
export { localeMap, DefaultLocale, getAvailableLocales }
