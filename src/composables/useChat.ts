import { ref, computed, watch } from 'vue';
import type { Ref } from 'vue';
import type { Message, Session, ChatState } from '@/types';
import { useApi } from '@/composables/useApi';

// Global chat state - strict separation
const chatState = ref<ChatState>({
  sessions: [],                    // For sidebar only
  selectedSessionId: null,         // Currently selected session
  currentMessages: [],             // Messages for selected session
  isLoading: false,
  error: null,
  mode: 'conversation'
});

// Track if sessions have been loaded at least once
const sessionsInitialized = ref(false);

export function useChat() {
  // Initialize the API composable
  const api = useApi();

  // Computed properties
  const selectedSession = computed(() =>
    chatState.value.sessions.find(s => s.id === chatState.value.selectedSessionId)
  );

  const currentMessages = computed(() =>
    chatState.value.currentMessages
  );

  const sessionCount = computed(() =>
    chatState.value.sessions.length
  );

  // Actions - Strict Separation

  // Load sessions for sidebar (no messages) - no global loading state
  const loadSessions = async () => {
    console.log('Chat: Loading sessions for sidebar');
    const response = await api.getSessions();

    // Store sessions directly (no conversion)
    chatState.value.sessions = response.data;
    sessionsInitialized.value = true;
    console.log('Chat: Loaded', response.data.length, 'sessions for sidebar');
  };

  // Select session and load its messages (strict separation)
  const selectSession = async (sessionId: string) => {
    // Prevent selecting the same session or multiple simultaneous selections
    if (chatState.value.selectedSessionId === sessionId || chatState.value.isLoading) {
      console.log('Chat: Skipping session selection - already selected or loading');
      return;
    }

    try {
      console.log('Chat: Selecting session and loading messages:', sessionId);
      chatState.value.isLoading = true;
      chatState.value.error = null;

      // Set selected session ID
      chatState.value.selectedSessionId = sessionId;

      // Load messages directly (no session metadata needed)
      const messagesResponse = await api.loadConversationMessages(sessionId);
      console.log('Messages: ', messagesResponse.data);
      const messages = messagesResponse.data || [];

      // Store messages directly in state (strict separation)
      chatState.value.currentMessages = messages;
      console.log('Chat: Loaded', messages.length, 'messages for session:', sessionId);

    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to load messages';
      console.error('Error selecting session:', error);
      // Clear messages on error
      chatState.value.currentMessages = [];
    } finally {
      chatState.value.isLoading = false;
    }
  };

  // Create new session (strict separation)
  const createNewSession = async (title?: string, initialMessage?: string) => {
    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      console.log('Creating new session with title:', title, 'initialMessage:', initialMessage);

      const response = await api.createConversation({ title, initialMessage });
      const newConversation = response.data.conversation;

      // Extract session data (strict separation)
      const newSession: Session = {
        id: newConversation.id,
        session_name: newConversation.title,
        created_at: newConversation.timestamp
      };

      // Add session to sessions list
      const existingIndex = chatState.value.sessions.findIndex(s => s.id === newSession.id);
      if (existingIndex === -1) {
        chatState.value.sessions.unshift(newSession);
      } else {
        chatState.value.sessions[existingIndex] = newSession;
      }

      // Select the new session and set its messages
      chatState.value.selectedSessionId = newSession.id;
      chatState.value.currentMessages = newConversation.messages || [];

      console.log('Successfully created session:', newSession.id);
      return newSession;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to create session';
      console.error('Error creating session:', error);
      throw error;
    } finally {
      chatState.value.isLoading = false;
    }
  };

  // Send message to session (strict separation)
  const sendMessage = async (content: string, sessionId?: string) => {
    // Use provided sessionId or fall back to selected session
    const targetSessionId = sessionId || chatState.value.selectedSessionId;

    // If no session exists, create one automatically
    if (!targetSessionId) {
      console.log('No session found, creating new session...');
      const newSession = await createNewSession('New Chat', content);
      if (!newSession) {
        throw new Error('Failed to create session');
      }
      return;
    }

    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      console.log('Sending message to session:', targetSessionId);

      const response = await api.sendMessage({
        conversationId: targetSessionId,
        content
      });

      // Update current messages directly (strict separation)
      if (chatState.value.selectedSessionId === targetSessionId) {
        // Add user message
        chatState.value.currentMessages.push(response.data.message);

        // Add AI response if available
        if (response.data.aiResponse) {
          chatState.value.currentMessages.push(response.data.aiResponse);
        }
      }

      // Ensure the session is selected if it wasn't already
      if (chatState.value.selectedSessionId !== targetSessionId) {
        chatState.value.selectedSessionId = targetSessionId;
        // Reload messages for the newly selected session
        await selectSession(targetSessionId);
      }

      return response.data;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to send message';
      console.error('Error sending message:', error);
      throw error;
    } finally {
      chatState.value.isLoading = false;
    }
  };

  // Search sessions (strict separation)
  const searchSessions = async (query: string) => {
    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      const response = await api.searchConversations(query);
      return response.data;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to search sessions';
      console.error('Error searching sessions:', error);
      return [];
    } finally {
      chatState.value.isLoading = false;
    }
  };

  // Rename session (strict separation) - no global loading state
  const renameSession = async (sessionId: string, newTitle: string) => {
    if (!newTitle.trim()) {
      throw new Error('Session title cannot be empty');
    }

    console.log('Renaming session:', sessionId, 'to:', newTitle);

    const response = await api.renameConversation(sessionId, newTitle.trim());

    // Update the session in local state
    const session = chatState.value.sessions.find(s => s.id === sessionId);
    if (session) {
      session.session_name = newTitle.trim();
    }

    console.log('Successfully renamed session:', sessionId);
    return response.data;
  };

  // Delete session (strict separation) - no global loading state
  const deleteSession = async (sessionId: string) => {
    console.log('Deleting session:', sessionId);

    await api.deleteConversation(sessionId);

    // Remove the session from local state
    const index = chatState.value.sessions.findIndex(s => s.id === sessionId);
    if (index !== -1) {
      chatState.value.sessions.splice(index, 1);
    }

    // If the deleted session was selected, clear selection and messages
    if (chatState.value.selectedSessionId === sessionId) {
      chatState.value.selectedSessionId = null;
      chatState.value.currentMessages = [];
    }

    console.log('Successfully deleted session:', sessionId);
  };

  // Utility functions
  const setMode = (mode: 'conversation' | 'document') => {
    chatState.value.mode = mode;
  };

  const clearError = () => {
    chatState.value.error = null;
  };

  const clearLoadingState = () => {
    chatState.value.isLoading = false;
    chatState.value.error = null;
  };

  // Initialize on first use
  const initialize = async () => {
    if (chatState.value.sessions.length === 0) {
      await loadSessions();
    }
  };

  // Set selected session by ID (for route-based selection)
  const setSelectedSessionId = async (sessionId: string | null) => {
    if (!sessionId) {
      chatState.value.selectedSessionId = null;
      chatState.value.currentMessages = [];
      return;
    }

    // If session is already selected, don't reload
    if (chatState.value.selectedSessionId === sessionId) {
      return;
    }

    // Check if session exists in loaded sessions
    const existingSession = chatState.value.sessions.find(s => s.id === sessionId);
    if (!existingSession) {
      console.error('Session not found in loaded sessions:', sessionId);
      return;
    }

    // Load messages for the session
    await selectSession(sessionId);
  };

  // Validate if a session exists in the loaded sessions
  const validateSessionExists = (sessionId: string): boolean => {
    return chatState.value.sessions.some(s => s.id === sessionId);
  };

  // Check if sessions are loaded (have been fetched at least once)
  const areSessionsLoaded = (): boolean => {
    return sessionsInitialized.value;
  };

  return {
    // State - Strict Separation
    chatState: computed(() => chatState.value),
    sessions: computed(() => chatState.value.sessions),
    selectedSession,
    selectedSessionId: computed(() => chatState.value.selectedSessionId),
    currentMessages,
    sessionCount,
    isLoading: computed(() => chatState.value.isLoading),
    error: computed(() => chatState.value.error),
    mode: computed(() => chatState.value.mode),

    // Actions - Strict Separation
    loadSessions,
    selectSession,
    createNewSession,
    sendMessage,
    renameSession,
    deleteSession,
    searchSessions,
    setMode,
    clearError,
    clearLoadingState,
    initialize,
    setSelectedSessionId,
    validateSessionExists,
    areSessionsLoaded
  };
}
