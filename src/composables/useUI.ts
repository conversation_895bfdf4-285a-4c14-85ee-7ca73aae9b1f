import { ref, computed, watch } from 'vue';
import { useColorMode } from '@vueuse/core';
import type { UIState, Theme } from '@/types';

// Global UI state
const uiState = ref<UIState>({
  isSidebarCollapsed: false,
  selectedDocument: null,
  theme: 'dark',
  isDocumentPreviewOpen: false
});

export function useUI() {
  // Theme management using VueUse
  const colorMode = useColorMode({
    modes: {
      light: 'light',
      dark: 'dark',
    }
  });

  // Computed properties
  const isSidebarCollapsed = computed(() => uiState.value.isSidebarCollapsed);
  const selectedDocument = computed(() => uiState.value.selectedDocument);
  const isDocumentPreviewOpen = computed(() => uiState.value.isDocumentPreviewOpen);
  const currentTheme = computed(() => colorMode.value as Theme);

  // Actions
  const toggleSidebar = () => {
    uiState.value.isSidebarCollapsed = !uiState.value.isSidebarCollapsed;
  };

  const collapseSidebar = () => {
    uiState.value.isSidebarCollapsed = true;
  };

  const expandSidebar = () => {
    uiState.value.isSidebarCollapsed = false;
  };

  const setSidebarCollapsed = (collapsed: boolean) => {
    uiState.value.isSidebarCollapsed = collapsed;
  };

  const openDocumentPreview = (documentName: string) => {
    uiState.value.selectedDocument = documentName;
    uiState.value.isDocumentPreviewOpen = true;
  };

  const closeDocumentPreview = () => {
    uiState.value.selectedDocument = null;
    uiState.value.isDocumentPreviewOpen = false;
  };

  const setTheme = (theme: Theme) => {
    colorMode.value = theme;
    uiState.value.theme = theme;
  };

  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark'];
    const currentIndex = themes.indexOf(currentTheme.value);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  // Responsive breakpoint helpers
  const isMobile = ref(false);
  const isTablet = ref(false);
  const isDesktop = ref(false);

  const updateBreakpoints = () => {
    const width = window.innerWidth;
    isMobile.value = width < 768;
    isTablet.value = width >= 768 && width < 1024;
    isDesktop.value = width >= 1024;
    
    // Auto-collapse sidebar on mobile
    if (isMobile.value && !uiState.value.isSidebarCollapsed) {
      uiState.value.isSidebarCollapsed = true;
    }
  };

  // Initialize breakpoints
  if (typeof window !== 'undefined') {
    updateBreakpoints();
    window.addEventListener('resize', updateBreakpoints);
  }

  // Watch for theme changes and apply to document
  watch(currentTheme, (newTheme) => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(newTheme);
    }
  }, { immediate: true });

  // Keyboard shortcuts
  const handleKeyboardShortcuts = (event: KeyboardEvent) => {
    // Ctrl/Cmd + B to toggle sidebar
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault();
      toggleSidebar();
    }
    
    // Ctrl/Cmd + D to toggle theme
    if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
      event.preventDefault();
      toggleTheme();
    }
    
    // Escape to close document preview
    if (event.key === 'Escape' && uiState.value.isDocumentPreviewOpen) {
      closeDocumentPreview();
    }
  };

  // Initialize keyboard shortcuts
  if (typeof window !== 'undefined') {
    window.addEventListener('keydown', handleKeyboardShortcuts);
  }

  // Cleanup function
  const cleanup = () => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', updateBreakpoints);
      window.removeEventListener('keydown', handleKeyboardShortcuts);
    }
  };

  return {
    // State
    uiState: computed(() => uiState.value),
    isSidebarCollapsed,
    selectedDocument,
    isDocumentPreviewOpen,
    currentTheme,
    isMobile: computed(() => isMobile.value),
    isTablet: computed(() => isTablet.value),
    isDesktop: computed(() => isDesktop.value),
    
    // Actions
    toggleSidebar,
    collapseSidebar,
    expandSidebar,
    setSidebarCollapsed,
    openDocumentPreview,
    closeDocumentPreview,
    setTheme,
    toggleTheme,
    cleanup
  };
}
