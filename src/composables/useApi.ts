import { ref, computed } from 'vue'
import type {
  Session,
  Conversation,
  Message,
  Document,
  ApiResponse,
  ApiError,
  SendMessageRequest,
  SendMessageResponse,
  CreateConversationRequest,
  CreateConversationResponse,
  UploadDocumentRequest,
  UploadDocumentResponse,
  SessionResponse,
  MessageResponse,
} from '@/types'
import {
  getAllSessions,
  getSession,
  getSessionHistory,
  createSession,
  updateSessionName,
  deleteSession,
  chat,
  ragUpload,
} from '@/services/api'

// Create API response wrapper
const createApiResponse = <T>(data: T, success = true, message?: string): ApiResponse<T> => ({
  data,
  success,
  message,
  timestamp: new Date().toISOString()
});

// Create API error
const createApiError = (code: string, message: string, details?: any): ApiError => ({
  code,
  message,
  details,
  timestamp: new Date().toISOString()
});

// Helper function to convert API message format to internal Message format
const convertMessage = (msg: MessageResponse): Message => ({
  id: msg.id,
  content: msg.content,
  role: msg.role,
  timestamp: msg.created_at
});

// Helper function to convert SessionResponse to Session (for sidebar)
const convertToSession = (sessionResponse: SessionResponse): Session => ({
  id: sessionResponse.id,
  session_name: sessionResponse.session_name || 'New Conversation',
  created_at: sessionResponse.created_at,
});

// Helper function to convert Session to Conversation (without messages)
const convertSessionToConversation = (session: Session): Conversation => ({
  id: session.id,
  title: session.session_name || 'New Conversation',
  timestamp: session.created_at,
  messages: [], // Empty array for lazy loading
});

// Helper function to create full conversation from session + messages
const createFullConversation = (session: Session, messages: Message[]): Conversation => ({
  id: session.id,
  title: session.session_name || 'New Conversation',
  timestamp: session.created_at,
  messages,
});

export function useApi() {
  // Reactive state
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Helper to handle API calls with loading and error states
  const handleApiCall = async <T>(apiCall: () => Promise<T>): Promise<T> => {
    loading.value = true
    error.value = null
    
    try {
      const result = await apiCall()
      return result
    } catch (err: any) {
      error.value = err.message || 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Get all sessions (for sidebar display - no messages)
  const getSessions = async (): Promise<ApiResponse<Session[]>> => {
    return handleApiCall(async () => {
      try {
        const response = await getAllSessions();
        const sessionResponses = response.data.data;

        // Convert to Session objects (no messages)
        const sessions: Session[] = sessionResponses.map((sessionResponse: SessionResponse) =>
          convertToSession(sessionResponse)
        );

        return createApiResponse(sessions, true, 'Sessions fetched successfully');
      } catch (error: any) {
        console.error('Error fetching sessions:', error);
        throw createApiError('FETCH_ERROR', 'Failed to fetch sessions', error);
      }
    })
  }

  // Get session metadata by ID (independent of messages)
  const getSessionById = async (id: string): Promise<ApiResponse<Session | null>> => {
    return handleApiCall(async () => {
      try {
        console.log('API: Loading session metadata for ID:', id);

        const sessionResponse = await getSession(id);
        const sessionData = sessionResponse.data.data;

        if (!sessionData) {
          console.log('API: Session not found for ID:', id);
          return createApiResponse(null, false, 'Session not found');
        }

        // Convert to Session object
        const session = convertToSession(sessionData);
        console.log('API: Session metadata loaded for:', session.session_name);

        return createApiResponse(session, true, 'Session found');
      } catch (error: any) {
        console.error('Error fetching session:', error);
        throw createApiError('FETCH_ERROR', 'Failed to fetch session', error);
      }
    })
  }

  // Load messages for a specific session (on-demand loading)
  const loadConversationMessages = async (conversationId: string): Promise<ApiResponse<Message[]>> => {
    return handleApiCall(async () => {
      try {
        const historyResponse = await getSessionHistory(conversationId);
        let messagesData: MessageResponse[];
        messagesData = historyResponse.data.data;

        const messages: Message[] = messagesData.map((msg: MessageResponse) =>
          convertMessage(msg)
        );

        console.log(messages)
        return createApiResponse(messages, true, 'Messages loaded successfully');
      } catch (error: any) {
        console.error('Error loading session messages:', error);
        throw createApiError('FETCH_ERROR', 'Failed to load session messages', error);
      }
    })
  }

  // Create new session (returns conversation format for compatibility)
  const createConversation = async (request: CreateConversationRequest): Promise<ApiResponse<CreateConversationResponse>> => {
    return handleApiCall(async () => {
      try {
        const response = await createSession();
        const sessionData = response.data.data;

        const messages: Message[] = [];
        
        // If there's an initial message, send it to the chat API
        if (request.initialMessage) {
          const userMessage: Message = {
            id: `${sessionData.id}-msg-1`,
            content: request.initialMessage,
            role: 'user',
            timestamp: new Date().toISOString()
          };
          messages.push(userMessage);

          try {
            // Send the initial message to get AI response
            const chatMessages: Message[] = [userMessage];
            await chat(sessionData.id, chatMessages, request.collection_name || 'default');

            // For streaming responses, we'll add a placeholder AI message
            const aiMessage: Message = {
              id: `${sessionData.id}-msg-2`,
              content: 'Thinking...',
              role: 'assistant',
              timestamp: new Date().toISOString()
            };
            messages.push(aiMessage);
          } catch (chatError) {
            console.error('Error sending initial message:', chatError);
          }
        }

        // Update session name if provided
        if (request.title && request.title !== 'New Conversation') {
          try {
            await updateSessionName(sessionData.id, request.title);
          } catch (nameError) {
            console.error('Error updating session name:', nameError);
          }
        }

        // Convert SessionResponse to Session first
        const session = convertToSession(sessionData);

        // Create conversation object with messages (if any)
        const conversation = createFullConversation(session, messages);

        // Update title if provided and different from default
        if (request.title && request.title !== 'New Conversation') {
          conversation.title = request.title;
        }

        return createApiResponse({
          conversation,
          sessionId: sessionData.id
        }, true, 'Conversation created successfully');
      } catch (error: any) {
        console.error('Error creating conversation:', error);
        throw createApiError('CREATE_ERROR', 'Failed to create conversation', error);
      }
    })
  }

  // Send message
  const sendMessage = async (request: SendMessageRequest): Promise<ApiResponse<SendMessageResponse>> => {
    return handleApiCall(async () => {
      try {
        const userMessage: Message = {
          id: `${request.conversationId}-${Date.now()}`,
          content: request.content,
          role: 'user',
          timestamp: new Date().toISOString()
        };

        // Get conversation history to build context
        let conversationHistory: Message[] = [];
        try {
          const historyResponse = await getSessionHistory(request.conversationId);

          // Handle both wrapped and direct array responses
          let messagesData: MessageResponse[];
          if (historyResponse.data && Array.isArray(historyResponse.data)) {
            // Direct array response
            messagesData = historyResponse.data;
          } else if (historyResponse.data && historyResponse.data.data && Array.isArray(historyResponse.data.data)) {
            // Wrapped response
            messagesData = historyResponse.data.data;
          } else {
            console.error('API: Unexpected response format for messages:', historyResponse);
            messagesData = [];
          }

          conversationHistory = messagesData.map((msg: MessageResponse) =>
            convertMessage(msg)
          );
        } catch (historyError) {
          console.warn('Could not fetch conversation history:', historyError);
        }

        // Add the new user message to history
        conversationHistory.push({
          id: `${request.conversationId}-${Date.now()}`,
          role: 'user',
          content: request.content,
          timestamp: new Date().toISOString()
        });

        // Send to chat API
        const chatResponse = await chat(request.conversationId, conversationHistory, 'General');
        
        // For streaming responses, create a placeholder AI message
        const aiMessage: Message = {
          id: `${request.conversationId}-${Date.now() + 1}`,
          content: 'Thinking...',
          role: 'assistant',
          timestamp: new Date().toISOString(),
          isLoading: true
        };

        return createApiResponse({ 
          message: userMessage, 
          aiResponse: aiMessage,
          streamResponse: chatResponse
        }, true, 'Message sent successfully');
      } catch (error: any) {
        console.error('Error sending message:', error);
        throw createApiError('SEND_ERROR', 'Failed to send message', error);
      }
    })
  }

  // Upload documents
  const uploadDocuments = async (request: UploadDocumentRequest): Promise<ApiResponse<UploadDocumentResponse>> => {
    return handleApiCall(async () => {
      try {
        const uploadedDocuments: Document[] = [];

        // Upload each file using the ragUpload API
        for (const file of request.files) {
          try {
            await ragUpload(file);

            uploadedDocuments.push({
              name: file.name,
              type: getDocumentType(file.name),
              size: file.size,
              uploadedAt: new Date().toISOString(),
              content: `Uploaded: ${file.name}`
            });
          } catch (uploadError) {
            console.error(`Error uploading file ${file.name}:`, uploadError);
          }
        }

        return createApiResponse({ documents: uploadedDocuments }, true, 'Documents uploaded successfully');
      } catch (error: any) {
        console.error('Error uploading documents:', error);
        throw createApiError('UPLOAD_ERROR', 'Failed to upload documents', error);
      }
    })
  }

  // Helper function to get document type
  const getDocumentType = (filename: string): 'pdf' | 'doc' | 'txt' | 'image' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf': return 'pdf';
      case 'doc':
      case 'docx': return 'doc';
      case 'txt': return 'txt';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif': return 'image';
      default: return 'pdf';
    }
  }

  // Search conversations
  const searchConversations = async (query: string): Promise<ApiResponse<Conversation[]>> => {
    return handleApiCall(async () => {
      try {
        // Get all sessions and filter them
        const response = await getAllSessions();
        const sessions = response.data.data;

        const filteredSessions = sessions.filter((session: SessionResponse) =>
          (session.session_name || '').toLowerCase().includes(query.toLowerCase())
        );

        const conversations: Conversation[] = filteredSessions.map((session: any) =>
          convertSessionToConversation(session)
        );

        return createApiResponse(conversations, true, 'Search completed successfully');
      } catch (error: any) {
        console.error('Error searching conversations:', error);
        throw createApiError('SEARCH_ERROR', 'Failed to search conversations', error);
      }
    })
  }

  // Rename conversation
  const renameConversation = async (conversationId: string, newTitle: string): Promise<ApiResponse<{ conversation: Conversation }>> => {
    return handleApiCall(async () => {
      try {
        await updateSessionName(conversationId, newTitle);

        // Get the updated session
        const sessionResponse = await getSessionById(conversationId);
        if (!sessionResponse.data) {
          throw createApiError('NOT_FOUND', 'Session not found');
        }

        // Convert to Conversation format
        const updatedConversation: Conversation = {
          id: sessionResponse.data.id,
          title: newTitle,
          timestamp: new Date().toISOString(),
          messages: [] // Empty for rename operation
        };

        return createApiResponse(
          { conversation: updatedConversation },
          true,
          'Conversation renamed successfully'
        );
      } catch (error: any) {
        console.error('Error renaming conversation:', error);
        throw createApiError('UPDATE_ERROR', 'Failed to rename conversation', error);
      }
    })
  }

  // Delete conversation
  const deleteConversation = async (conversationId: string): Promise<ApiResponse<{ success: boolean }>> => {
    return handleApiCall(async () => {
      try {
        await deleteSession(conversationId);

        return createApiResponse(
          { success: true },
          true,
          'Conversation deleted successfully'
        );
      } catch (error: any) {
        console.error('Error deleting conversation:', error);
        throw createApiError('DELETE_ERROR', 'Failed to delete conversation', error);
      }
    })
  }

  // Get document content (placeholder - can be extended)
  const getDocumentContent = async (documentName: string): Promise<ApiResponse<any>> => {
    return handleApiCall(async () => {
      try {
        // For now, return mock content since the old API doesn't have a specific document content endpoint
        const content = {
          pages: [{
            number: 1,
            content: `Content for ${documentName}`,
            highlights: []
          }]
        };

        return createApiResponse(content, true, 'Document content fetched successfully');
      } catch (error: any) {
        console.error('Error fetching document content:', error);
        throw createApiError('FETCH_ERROR', 'Failed to fetch document content', error);
      }
    })
  }

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Methods
    getSessions,
    getSessionById,
    loadConversationMessages,
    createConversation,
    sendMessage,
    uploadDocuments,
    searchConversations,
    renameConversation,
    deleteConversation,
    getDocumentContent,

    // Clear error
    clearError: () => { error.value = null }
  }
}
