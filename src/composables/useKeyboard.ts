import { onMounted, onUnmounted } from 'vue';

export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  meta?: boolean;
  shift?: boolean;
  alt?: boolean;
  callback: (event: KeyboardEvent) => void;
  preventDefault?: boolean;
  description?: string;
}

export function useKeyboard() {
  const shortcuts = new Map<string, KeyboardShortcut>();

  const generateShortcutKey = (shortcut: KeyboardShortcut): string => {
    const parts = [];
    if (shortcut.ctrl) parts.push('ctrl');
    if (shortcut.meta) parts.push('meta');
    if (shortcut.shift) parts.push('shift');
    if (shortcut.alt) parts.push('alt');
    parts.push(shortcut.key.toLowerCase());
    return parts.join('+');
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    const key = event.key.toLowerCase();
    const shortcutKey = [
      event.ctrlKey && 'ctrl',
      event.metaKey && 'meta',
      event.shiftKey && 'shift',
      event.altKey && 'alt',
      key
    ].filter(Boolean).join('+');

    const shortcut = shortcuts.get(shortcutKey);
    if (shortcut) {
      if (shortcut.preventDefault !== false) {
        event.preventDefault();
      }
      shortcut.callback(event);
    }
  };

  const addShortcut = (shortcut: KeyboardShortcut) => {
    const key = generateShortcutKey(shortcut);
    shortcuts.set(key, shortcut);
  };

  const removeShortcut = (shortcut: KeyboardShortcut) => {
    const key = generateShortcutKey(shortcut);
    shortcuts.delete(key);
  };

  const addShortcuts = (shortcutList: KeyboardShortcut[]) => {
    shortcutList.forEach(addShortcut);
  };

  const removeShortcuts = (shortcutList: KeyboardShortcut[]) => {
    shortcutList.forEach(removeShortcut);
  };

  const clearShortcuts = () => {
    shortcuts.clear();
  };

  const getShortcuts = () => {
    return Array.from(shortcuts.values());
  };

  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
  });

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown);
    clearShortcuts();
  });

  return {
    addShortcut,
    removeShortcut,
    addShortcuts,
    removeShortcuts,
    clearShortcuts,
    getShortcuts
  };
}

// Common keyboard shortcuts
export const commonShortcuts = {
  // Navigation
  escape: (callback: () => void): KeyboardShortcut => ({
    key: 'Escape',
    callback,
    description: 'Close modal or cancel action'
  }),

  // App shortcuts
  toggleSidebar: (callback: () => void): KeyboardShortcut => ({
    key: 'b',
    ctrl: true,
    callback,
    description: 'Toggle sidebar'
  }),

  toggleTheme: (callback: () => void): KeyboardShortcut => ({
    key: 'd',
    ctrl: true,
    callback,
    description: 'Toggle dark/light theme'
  }),

  newConversation: (callback: () => void): KeyboardShortcut => ({
    key: 'n',
    ctrl: true,
    callback,
    description: 'Start new conversation'
  }),

  search: (callback: () => void): KeyboardShortcut => ({
    key: 'k',
    ctrl: true,
    callback,
    description: 'Open search'
  }),

  // Text editing
  selectAll: (callback: () => void): KeyboardShortcut => ({
    key: 'a',
    ctrl: true,
    callback,
    description: 'Select all text'
  }),

  copy: (callback: () => void): KeyboardShortcut => ({
    key: 'c',
    ctrl: true,
    callback,
    description: 'Copy text'
  }),

  paste: (callback: () => void): KeyboardShortcut => ({
    key: 'v',
    ctrl: true,
    callback,
    description: 'Paste text'
  }),

  // Chat shortcuts
  sendMessage: (callback: () => void): KeyboardShortcut => ({
    key: 'Enter',
    callback,
    description: 'Send message'
  }),

  newLine: (callback: () => void): KeyboardShortcut => ({
    key: 'Enter',
    shift: true,
    callback,
    preventDefault: false,
    description: 'New line in message'
  })
};
