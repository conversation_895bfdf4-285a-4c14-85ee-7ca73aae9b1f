@tailwind base;
@tailwind components;
@tailwind utilities;

/* Element Plus theme integration */
:root {
  --el-color-primary: hsl(var(--primary));
  --el-color-primary-light-3: hsl(var(--primary-light));
  --el-color-primary-light-5: hsl(var(--primary-glow));
  --el-color-primary-dark-2: hsl(var(--primary));
  --el-bg-color: hsl(var(--background));
  --el-bg-color-page: hsl(var(--background));
  --el-text-color-primary: hsl(var(--foreground));
  --el-text-color-regular: hsl(var(--muted-foreground));
  --el-border-color: hsl(var(--border));
  --el-border-color-light: hsl(var(--border));
  --el-fill-color: hsl(var(--muted));
  --el-fill-color-light: hsl(var(--muted));
}

.dark {
  --el-bg-color: hsl(var(--background));
  --el-bg-color-page: hsl(var(--background));
  --el-text-color-primary: hsl(var(--foreground));
  --el-text-color-regular: hsl(var(--muted-foreground));
  --el-border-color: hsl(var(--border));
  --el-border-color-light: hsl(var(--border));
  --el-fill-color: hsl(var(--muted));
  --el-fill-color-light: hsl(var(--muted));
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Purple theme for chatbot */
    --primary: 263 85% 60%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 263 65% 75%;
    --primary-glow: 263 85% 85%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 263 20% 96%;
    --accent-foreground: 263 85% 60%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 263 85% 60%;

    /* Chat specific colors */
    --chat-bubble-user: 263 85% 60%;
    --chat-bubble-user-foreground: 210 40% 98%;
    --chat-bubble-ai: 220 14.3% 95.9%;
    --chat-bubble-ai-foreground: 222.2 84% 4.9%;

    /* Sidebar colors */
    --sidebar-bg: 220 14.3% 97%;
    --sidebar-border: 220 13% 91%;
    --sidebar-hover: 263 20% 96%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Vue-specific styles */
  #root {
    @apply min-h-screen;
  }

  /* Ensure proper scrolling */
  html, body {
    @apply overflow-hidden;
  }

  /* Custom scrollbar styles */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border/80;
  }

  /* Focus styles for accessibility */
  .focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2;
  }

  /* Vue transition utilities */
  .v-enter-active,
  .v-leave-active {
    transition: all 0.3s ease;
  }

  .v-enter-from,
  .v-leave-to {
    opacity: 0;
    transform: translateY(10px);
  }
}

/* Markdown Content Styles */
@layer components {
  .markdown-content {
    @apply text-sm leading-relaxed;
  }

  .markdown-content h1,
  .markdown-content h2,
  .markdown-content h3,
  .markdown-content h4,
  .markdown-content h5,
  .markdown-content h6 {
    @apply font-semibold text-foreground mt-6 mb-3 first:mt-0;
  }

  .markdown-content h1 { @apply text-2xl; }
  .markdown-content h2 { @apply text-xl; }
  .markdown-content h3 { @apply text-lg; }
  .markdown-content h4 { @apply text-base; }
  .markdown-content h5 { @apply text-sm; }
  .markdown-content h6 { @apply text-xs; }

  .markdown-content p {
    @apply mb-4 last:mb-0;
  }

  .markdown-content ul,
  .markdown-content ol {
    @apply mb-4 pl-6;
  }

  .markdown-content ul {
    @apply list-disc;
  }

  .markdown-content ol {
    @apply list-decimal;
  }

  .markdown-content li {
    @apply mb-1;
  }

  .markdown-content blockquote {
    @apply border-l-4 border-border pl-4 italic text-muted-foreground mb-4;
  }

  .markdown-content a {
    @apply text-primary hover:text-primary/80 underline;
  }

  .markdown-content strong,
  .markdown-content b {
    @apply font-semibold;
  }

  .markdown-content em,
  .markdown-content i {
    @apply italic;
  }

  .markdown-content code.inline-code {
    @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono;
  }

  .markdown-content table {
    @apply w-full border-collapse border border-border mb-4;
  }

  .markdown-content th,
  .markdown-content td {
    @apply border border-border px-3 py-2 text-left;
  }

  .markdown-content th {
    @apply bg-muted font-semibold;
  }

  .markdown-content hr {
    @apply border-0 border-t border-border my-6;
  }

  /* Code block wrapper styles */
  .hljs-code-wrapper {
    @apply mb-4 rounded-lg overflow-hidden border border-border;
  }

  .hljs-code-wrapper--header {
    @apply bg-muted px-4 py-2 text-xs font-mono text-muted-foreground border-b border-border;
  }

  .hljs-code-wrapper pre {
    @apply m-0 p-4 overflow-x-auto bg-[#0d1117] text-[#e6edf3];
  }

  .hljs-code-wrapper code {
    @apply font-mono text-sm;
  }

  /* MathJax styles */
  .markdown-content mjx-container {
    @apply my-2;
  }

  .markdown-content mjx-container[display="true"] {
    @apply my-4 text-center;
  }

  /* Metadata styles */
  .markdown-content .c_metadata {
    @apply inline-block bg-primary/10 text-primary px-2 py-1 rounded text-xs font-mono;
  }

  /* Ensure proper spacing */
  .markdown-content > *:first-child {
    @apply mt-0;
  }

  .markdown-content > *:last-child {
    @apply mb-0;
  }
}