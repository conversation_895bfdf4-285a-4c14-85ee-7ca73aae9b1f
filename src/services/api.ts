import { useAxios } from '@/lib/axios'
import type { Message, SessionResponse, MessageResponse, ApiResponseWrapper } from '@/types'


export async function chat(sessionId: string, messages: Message[], collection_name: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/assistant/chat-with-docs`
  const http = useAxios()

  const data = {
    "session_id": sessionId,
    "messages": messages,
    "collection_name": collection_name
  }
                                                                                                               
  return http.post(url, JSON.stringify(data), {
    headers: {
      'Content-Type': 'application/json'
    },
    adapter: "fetch",
    responseType: "stream",
  })
}

export async function getAllSessions(): Promise<{ data: ApiResponseWrapper<SessionResponse[]> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session`
  const http = useAxios()
  return http.get(url)
}

export async function getSession(sessionId: string): Promise<{ data: ApiResponseWrapper<SessionResponse> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}`
  const http = useAxios()
  return http.get(url)
}

export async function getSessionHistory(sessionId: string): Promise<{ data: ApiResponseWrapper<MessageResponse[]> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}/chat-message`
  const http = useAxios()
  return http.get(url)
}

export async function createSession(): Promise<{ data: ApiResponseWrapper<SessionResponse> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session`
  const http = useAxios()
  return http.post(url)
}

export async function updateSessionName(sessionId: string, updated_name: string): Promise<{ data: ApiResponseWrapper<any> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}?updated_name=${updated_name}`
  const http = useAxios()
  return http.post(url)
}

export async function deleteSession(sessionId: string): Promise<{ data: ApiResponseWrapper<any> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}`
  const http = useAxios()
  return http.delete(url)
}

export async function ragUpload(file: File): Promise<{ data: ApiResponseWrapper<any> }> {
  const url = `/${import.meta.env.VITE_API_VERSION}/rag/upload_file`
  const http = useAxios()

  return http.post(url, { input_file: file }, {
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}
