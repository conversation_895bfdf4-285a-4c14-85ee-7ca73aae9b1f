import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

// Import pages
import Index from '@/pages/Index.vue';
import NotFound from '@/pages/NotFound.vue';

// Define routes
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/chat'
  },
  {
    path: '/chat/:id?',
    name: 'Chat',
    component: Index,
    props: true,
    meta: {
      title: 'Chat Helper Bot',
      description: 'AI-powered chat assistant for your documents'
    }
  },

  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: 'Page Not Found - Chat Helper Bot',
      description: 'The page you are looking for does not exist'
    }
  }
];

// Create router instance
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // Always scroll to top when changing routes
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// Navigation guards
router.beforeEach((to, from, next) => {
  // Update document title
  if (to.meta?.title) {
    document.title = to.meta.title as string;
  }

  // Update meta description
  const metaDescription = document.querySelector('meta[name="description"]');
  if (metaDescription && to.meta?.description) {
    metaDescription.setAttribute('content', to.meta.description as string);
  }

  next();
});

router.afterEach((to, from) => {
  if (typeof to.meta.title === 'string') {
      document.title = to.meta.title;
  }
});

// Error handling
router.onError((error) => {
  console.error('Router error:', error);
});

export default router;
