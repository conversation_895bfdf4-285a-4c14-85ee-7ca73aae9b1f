// Core data types for the Vue 3 chat application

export interface Document {
  name: string;
  pages?: string;
  content?: string;
  type?: 'pdf' | 'doc' | 'txt' | 'image';
  size?: number;
  uploadedAt?: string;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  documents?: Document[];
  isLoading?: boolean;
  error?: string;
  reasoning?: ReasoningData;
}

export interface ReasoningData {
  id: string;
  content: string;
  loading: boolean;
  timeRange: number;
}

// Session interface - for sidebar display only (no messages)
export interface Session {
  id: string;
  session_name: string;
  created_at: string;
}

// Conversation interface - for chat interface display with messages
export interface Conversation {
  id: string;
  title: string;
  timestamp: string;
  messages: Message[];
}

export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  role?: 'user' | 'admin';
}

export interface ChatState {
  sessions: Session[];              // For sidebar only
  selectedSessionId: string | null; // Currently selected session
  currentMessages: Message[];       // Messages for selected session
  isLoading: boolean;
  error: string | null;
  mode: 'conversation' | 'document';
}

export interface UIState {
  isSidebarCollapsed: boolean;
  selectedDocument: string | null;
  theme: 'light' | 'dark';
  isDocumentPreviewOpen: boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// API response wrapper schema - what the API actually returns
export interface ApiResponseWrapper<T> {
  code: string;
  message: string;
  timestamp: string;
  data: T;
}

// API response schemas - exact match for backend API
export interface SessionResponse {
  id: string;
  session_name: string;
  created_at: string;
}

export interface MessageResponse {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  session_id: string;
  created_at: string;
}

// API request/response types
export interface SendMessageRequest {
  conversationId: string;  // Keep for API compatibility - represents sessionId
  content: string;
  documents?: File[];
}

export interface SendMessageResponse {
  message: Message;
  aiResponse?: Message;
  streamResponse?: any; // For handling streaming responses from the chat API
}

export interface CreateConversationRequest {
  title?: string;
  initialMessage?: string;
  collection_name?: string;
}

export interface CreateConversationResponse {
  conversation: Conversation;  // Keep for API compatibility - contains session + messages
}

export interface UploadDocumentRequest {
  conversationId: string;
  files: File[];
}

export interface UploadDocumentResponse {
  documents: Document[];
}

// Component prop types
export interface ChatMessageProps {
  message: Message;
  onDocumentClick?: (documentName: string) => void;
}

export interface ChatInputProps {
  onSendMessage?: (content: string, files?: File[]) => void;
  onFileUpload?: (files: File[]) => void;
  disabled?: boolean;
  placeholder?: string;
  disableDragOverlay?: boolean; // Disable drag overlay when parent handles it
}

export interface ChatSidebarProps {
  selectedConversation?: string;  // Keep for backward compatibility with template
  onSelectConversation?: (id: string) => void;
  onNewConversation?: () => void;
  mode?: 'conversation' | 'document';
  onModeChange?: (mode: 'conversation' | 'document') => void;
  isSidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

export interface ChatHeaderProps {
  title?: string;
}

export interface ChatInterfaceProps {
  conversationId?: string;  // Keep for backward compatibility - represents sessionId
}

export interface DocumentPreviewProps {
  documentName: string;
  onClose: () => void;
}

export interface ReasoningMessageProps {
  id: string;
  reasoningContent: string;
  loading: boolean;
  timeRange: number;
}

// Utility types
export type MessageSender = 'user' | 'assistant';
export type AppMode = 'conversation' | 'document';
export type Theme = 'light' | 'dark';
export type DocumentType = 'pdf' | 'doc' | 'txt' | 'image';

// Event types
export interface ChatEvents {
  'message:send': SendMessageRequest;
  'message:receive': Message;
  'conversation:select': string;
  'conversation:create': CreateConversationRequest;
  'document:upload': UploadDocumentRequest;
  'document:preview': string;
  'sidebar:toggle': boolean;
  'theme:change': Theme;
}
