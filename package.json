{"name": "vue3_chat_helper_bot", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tanstack/vue-query": "^5.56.2", "@vueuse/core": "^11.0.3", "@vueuse/head": "^2.0.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "element-plus": "^2.10.4", "highlight.js": "^11.11.1", "juice": "^11.0.1", "lucide-vue-next": "^0.462.0", "markdown-it": "^14.1.0", "mathjax-full": "^3.2.2", "radix-vue": "^1.9.6", "remixicon": "^4.6.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vue": "^3.5.12", "vue-i18n": "^9.14.5", "vue-router": "^4.4.5", "vue-sonner": "^1.2.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/dompurify": "^3.0.5", "@types/markdown-it": "^14.1.2", "@types/node": "^22.5.5", "@vitejs/plugin-vue": "^5.1.4", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "vite": "^5.4.1", "vue-tsc": "^2.1.6"}}