#!/bin/sh
function join_by { local IFS="$1"; shift; echo "$*"; }
# Find vue env vars
vars=$(env | grep VITE_APP_ | awk -F = '{print "$"$1}')
vars=$(join_by ',' $vars)
echo "Found variables $vars"

ROOT_DIR=/var/www/html

for file in $ROOT_DIR/assets/index*.*;
do
  echo "Processing $file ...";
  if [ ! -f $file.tmpl.js ]; then
    cp $file $file.tmpl.js
  fi
  envsubst "$vars" < $file.tmpl.js > $file
done

cp $ROOT_DIR/index.html $ROOT_DIR/index.tmpl.html
envsubst "$vars" < $ROOT_DIR/index.tmpl.html > $ROOT_DIR/index.html

envsubst "$vars" < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf
echo "Starting Nginx"
nginx -g 'daemon off;'
