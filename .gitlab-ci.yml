#========================
# Define global
#========================
# include:
  # - template: Jobs/SAST.latest.gitlab-ci.yml
  # - template: Jobs/Secret-Detection.gitlab-ci.yml


stages:
  - test
  - build
  - deploy


variables:
  GIT_DEPTH: 1
  GIT_STRATEGY: fetch


#========================
# Define snips
#========================
.build-with-buildx:
  image: docker:latest
  services:
    - docker:dind
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: /certs
    DOCKER_HOST: tcp://docker:2376
    BUILDER_CONTEXT: mycontext
    BUILDER_NAME:  mybuilder
    BUILDER_PLATFORMS: linux/amd64,linux/arm64
  before_script:
    - | # Create docker context, builder using buildx
      docker context create ${BUILDER_CONTEXT}
      docker context use ${BUILDER_CONTEXT}
      docker buildx create --driver=docker-container --platform=${BUILDER_PLATFORMS} --name=${BUILDER_NAME} --use ${BUILDER_CONTEXT}
      docker buildx use ${BUILDER_NAME}
    - | # login registry
      docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  after_script:
    - | # Logout registry
      docker logout ${CI_REGISTRY} || exit 0
    - | # Remove docker builder
      docker buildx rm ${BUILDER_NAME} || exit 0
      docker context rm ${BUILDER_CONTEXT} || exit 0
  script:
    - | # Setup build tags
      export IMAGE_REPO=${CI_REGISTRY}/tech/ai-engine/rabiai-demo/rabiai-demo-fe
      export IMAGE_COMMIT_TAG=${CI_COMMIT_SHORT_SHA}
      if [[ -n "${CI_COMMIT_TAG}" ]]; then
        export IMAGE_PUSH_TAGS="${CI_COMMIT_TAG} latest"
      elif [[ $CI_COMMIT_REF_SLUG == 'master' ]]; then
        export IMAGE_PUSH_TAGS=latest
      else
        export IMAGE_PUSH_TAGS=$CI_COMMIT_REF_SLUG
      fi
    - echo ${IMAGE_COMMIT_TAG}
    - | # Build image 
      sed -i "s|^dist\/||g" .dockerignore
      docker buildx build \
        --cache-from=type=local,src=/tmp/dockerize-cache \
        --cache-to=type=local,dest=/tmp/dockerize-cache,mode=max \
        --file=docker/Dockerfile \
        --target=deploy-ci \
        --tag=${IMAGE_REPO}:${IMAGE_COMMIT_TAG} \
        --load \
        .
    - docker images
    - | # Push image tags
      docker push ${IMAGE_REPO}:${IMAGE_COMMIT_TAG}
      for IMAGE_PUSH_TAG in ${IMAGE_PUSH_TAGS}; do
        docker tag ${IMAGE_REPO}:${IMAGE_COMMIT_TAG} ${IMAGE_REPO}:${IMAGE_PUSH_TAG}
        docker push ${IMAGE_REPO}:${IMAGE_PUSH_TAG}
      done
  cache:
    key: ${CI_JOB_NAME}
    paths:
      - /tmp/dockerize-cache


.work-with-nodejs:
  image: node:18-alpine
  variables:
    PNPM_HOME: "/pnpm"
  before_script:
    - corepack enable
    - cp .env.example .env
    - sed -i "s|^VITE_API_URL=.*|VITE_API_URL=${VITE_API_URL}|g"       .env
    - sed -i "s|^VITE_API_VERSION=.*|VITE_API_VERSION=${VITE_API_VERSION}|g"       .env
    - sed -i "s|^VITE_SET_DOMAIN=.*|VITE_SET_DOMAIN=${VITE_SET_DOMAIN}|g"       .env
    - cat .env
  cache:
    key:
      files:
        - package.json
        - pnpm-lock.yaml
    paths:
      - /pnpm/store
    when: on_success


.deploy:
  image: alpine:latest
  script:
    - echo "URL"


#========================
# Define jobs
#========================
test:
  stage: test
  extends: .work-with-nodejs
  script:
    - pnpm install --frozen-lockfile
    - pnpm run lint
    - pnpm run test:unit
    - pnpm run build


build-stg-app:
  stage: build
  only:
    - develop
    - release/*
    - master
  environment:
    name: staging
  extends: .work-with-nodejs
  script:
    - pnpm install --frozen-lockfile
    - pnpm run build
  artifacts:
    paths:
      - ./dist
    when: on_success
    expire_in: "30 days"


build-prod-app:
  stage: build
  only:
    - tags
  environment:
    name: production
  extends: .work-with-nodejs
  script:
    - pnpm run build
  artifacts:
    paths:
      - ./dist
    when: on_success
    expire_in: "30 days"


build-stg-image:
  stage: build
  only:
    - develop
    - release/*
    - master
  when: manual
  needs:
    - job: build-stg-app
      artifacts: true
  environment:
    name: staging
  extends: .build-with-buildx


build-prod-image:
  stage: build
  only:
    - tags
  when: manual
  needs:
    - job: build-prod-app
      artifacts: true
  environment:
    name: production
  extends: .build-with-buildx


deploy-stg:
  stage: deploy
  only:
    - develop
    - release/*
    - master
  when: manual
  needs:
    - job: build-stg-image
      artifacts: false
  environment:
    name: staging
  extends: .deploy


deploy-prod:
  stage: deploy
  only:
    - tags
  when: manual
  needs:
    - job: build-prod-image
      artifacts: false
  environment:
    name: production
  extends: .deploy
