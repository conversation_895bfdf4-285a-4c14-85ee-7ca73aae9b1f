### Stage: base
FROM node:18-alpine AS base

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

WORKDIR /app


### Stage: dependencies
FROM base AS dependencies

COPY package.json pnpm-lock.yaml ./
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile


### Stage: build
FROM base AS build

ARG VITE_API_URL
ARG VITE_SET_DOMAIN
ARG VITE_API_VERSION

COPY .env.example .env
RUN sed -i "s|^VITE_API_URL=.*|VITE_API_URL=${VITE_API_URL}|g" .env
RUN sed -i "s|^VITE_API_VERSION=.*|VITE_API_VERSION=${VITE_API_VERSION}|g" .env
RUN sed -i "s|^VITE_SET_DOMAIN=.*|VITE_SET_DOMAIN=${VITE_SET_DOMAIN}|g" .env
RUN cat .env

COPY . .
COPY --from=dependencies /app/node_modules ./node_modules

RUN pnpm run build


### Stage: deploy
FROM nginx:stable-alpine AS deploy

COPY docker/default.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/dist /usr/share/nginx/html
RUN chmod +x /entrypoint.sh

ENTRYPOINT [ "/entrypoint.sh" ]

### Stage: deploy from CI
FROM nginx:stable-alpine AS deploy-ci

COPY docker/default.conf /etc/nginx/conf.d/default.conf
COPY dist /usr/share/nginx/html
